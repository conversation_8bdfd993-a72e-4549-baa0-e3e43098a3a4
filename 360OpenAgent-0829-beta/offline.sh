#!/bin/bash
#set -x

# 显示帮助信息
show_help() {
  echo "用法: $0 [选项]"
  echo
  echo "选项:"
  echo "  -h, --help                 显示此帮助信息"
  echo "  -p, --platform PLATFORM    指定要处理的平台 (all|amd64|arm64)"
  echo "                             默认值: all"
  echo
  echo "示例:"
  echo "  $0                         处理所有平台镜像"
  echo "  $0 -p amd64                仅处理 AMD64 平台镜像"
  echo "  $0 --platform arm64        仅处理 ARM64 平台镜像"
  echo
}

# 默认处理所有平台
PLATFORM="all"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    -h|--help)
      show_help
      exit 0
      ;;
    -p|--platform)
      if [[ -z "$2" || "$2" == -* ]]; then
        echo "错误: --platform 选项需要参数"
        exit 1
      fi
      PLATFORM="$2"
      shift 2
      ;;
    *)
      echo "错误: 未知参数 $1"
      show_help
      exit 1
      ;;
  esac
done

# 验证平台参数
if [[ "$PLATFORM" != "all" && "$PLATFORM" != "amd64" && "$PLATFORM" != "arm64" ]]; then
  echo "错误: 平台必须是 all、amd64 或 arm64"
  show_help
  exit 1
fi

echo "将处理平台: $PLATFORM"

# 定义镜像列表
images=(
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/elasticsearch:7.17.25"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/mysql:8.4.3"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/redis:7.4.2"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/mongo:4.4.29"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/minio:2024"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/prompt_frontend:v1.09"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/prompt_backend:v1.08"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/async_agentrunner:v1.04"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/agentapi_code:v1"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/web_crawl:v2"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/agent_logstash:v3"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/kibana:7.17.25"
#  "harbor.qihoo.net/ywpt-agentpt-360openagent/official-redis:7.4.2"
)
# 如果 images 为空，则从 docker-compose.yaml 中提取镜像名
if [ ${#images[@]} -eq 0 ]; then
  echo "镜像列表为空，尝试从 docker-compose-aliyun.yaml 中提取..."
  compose_file="docker/docker-compose-aliyun.yaml"

  if [ ! -f "$compose_file" ]; then
    echo "错误：找不到文件 $compose_file"
    exit 1
  fi

  # 显式声明为普通数组
  images=()

  # 使用临时文件方式，确保兼容所有shell
  temp_file=$(mktemp)
  # 更精确的正则表达式来匹配docker-compose中的image行
  grep -i '^ *image:' "$compose_file" > "$temp_file"
  
  echo "DEBUG: 提取到的原始镜像行："
  cat "$temp_file"
  echo "----------------------"
  
  while read -r line; do
    # 简化的镜像名提取方法
    img=$(echo "$line" | sed 's/^.*image:[[:space:]]*//' | sed 's/^[[:space:]]*//;s/[[:space:]]*$//' | tr -d '"' | tr -d "'")
    
    # 验证镜像名不为空且格式有效 - 简化的验证逻辑
    if [[ -n "$img" && ! " ${images[*]} " =~ " ${img} " ]]; then
      echo "DEBUG: 找到有效镜像: '$img'"
      images+=("$img")
    else
      echo "DEBUG: 跳过无效镜像定义: '$line' (提取结果: '$img')"
    fi
  done < "$temp_file"
  
  # 清理临时文件
  rm -f "$temp_file"

  # 调试输出：查看提取了多少个镜像
  echo "DEBUG: 成功从 $compose_file 提取到 ${#images[@]} 个镜像。"
fi

# 创建输出目录
if [[ "$PLATFORM" == "all" || "$PLATFORM" == "amd64" ]]; then
  mkdir -p platform/amd64
fi
if [[ "$PLATFORM" == "all" || "$PLATFORM" == "arm64" ]]; then
  mkdir -p platform/arm64
fi

# 打印获取到的所有镜像列表
echo "=============================="
echo "总共找到 ${#images[@]} 个镜像，以下是镜像列表："
echo "=============================="
for img in "${images[@]}"; do
  echo "- $img"
done
echo "=============================="
echo "开始处理镜像 (平台: $PLATFORM)..."
echo "-------------------"

# 处理每个镜像
for image in "${images[@]}"; do
  # 再次验证镜像名格式 - 简化的验证逻辑
  if [[ -z "$image" ]]; then
    echo "警告: 跳过空的镜像名称"
    continue
  fi

  echo "处理镜像: $image"

  # 生成文件名（将 / 和 : 替换为 _）
  filename=$(echo "$image" | tr '/:' '_')

  # AMD64 架构
  if [[ "$PLATFORM" == "all" || "$PLATFORM" == "amd64" ]]; then
    echo "拉取 AMD64 版本..."
    if docker pull --platform linux/amd64 "$image"; then
      docker save "$image" > "platform/amd64/${filename}.tar"
      docker rmi "$image"
    else
      echo "错误: 无法拉取 AMD64 版本的镜像 '$image'"
    fi
  fi

  # ARM64 架构
  if [[ "$PLATFORM" == "all" || "$PLATFORM" == "arm64" ]]; then
    echo "拉取 ARM64 版本..."
    if docker pull --platform linux/arm64 "$image"; then
      docker save "$image" > "platform/arm64/${filename}.tar"
      docker rmi "$image"
    else
      echo "错误: 无法拉取 ARM64 版本的镜像 '$image'"
    fi
  fi

  echo "完成: $image"
  echo "-------------------"
done

echo "所有镜像处理完成 (平台: $PLATFORM)"
