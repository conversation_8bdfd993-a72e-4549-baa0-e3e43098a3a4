x-shared-env: &shared-env
  ELASTIC<PERSON>ARCH_CLUSTER_NAME: ${ELAST<PERSON><PERSON>ARCH_CLUSTER_NAME:-private-cluster}
  ELASTICSEARCH_NODE_NAME: ${<PERSON>LASTICSEARCH_NODE_NAME:-agent_node}
  ELASTICSEARCH_USERNAME: ${ELA<PERSON><PERSON><PERSON>ARCH_USERNAME:-elastic}
  ELASTICSEARCH_PASSWORD: ${ELASTICSEARCH_PASSWORD:-espass}
  ELASTICSEARCH_ENABLE_SECURITY: ${ELASTICSEARCH_ENABLE_SECURITY:-false}
  ELASTICSEARCH_TLS_USE_PKCS12_KEYSTORE: ${ELASTICSEARCH_TLS_USE_PKCS12_KEYSTORE:-false}
  ELASTICSEARCH_TLS_USE_PKCS12_TRUSTSTORE: ${ELASTICSEARCH_TLS_USE_PKCS12_TRUSTSTORE:-false}
  MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123}
  MYSQL_DATABASE: ${MYSQL_DATABASE:-agent2_prd}
  MYSQL_USER: ${MYSQL_USER:-agent}
  MYSQL_PASSWORD: ${MYSQL_PASSWORD:-root123}
  REDIS_PASSWORD: ${REDIS_PASSWORD:-redispass}
  MONGO_INITDB_ROOT_USERNAME: ${MONGO_INITDB_ROOT_USERNAME:-admin}
  MONGO_INITDB_ROOT_PASSWORD: ${MONGO_INITDB_ROOT_PASSWORD:-123456}
  MONGODB_USERNAME: ${MONGODB_USERNAME:-admin}
  MONGODB_PASSWORD: ${MONGODB_PASSWORD:-123456}
  MONGODB_DATABASE: ${MONGODB_DATABASE:-agent}
  MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
  MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123456}
  env_channel: ${env_channel:-private}
  WEB_CONCURRENCY: ${WEB_CONCURRENCY:-2}
  env: ${env:-prd}


services:
  agent-elasticsearch:
    container_name: agent-elasticsearch
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/elasticsearch:7.17.25
    environment:
      - bootstrap.memory_lock=true
      - ES_JAVA_OPTS=-Xms8g -Xmx8g
      - discovery.type=single-node
      - http.cors.enabled=true
      - http.cors.allow-origin="*"
      - TAKE_FILE_OWNERSHIP=true
      - http.max_content_length=1204mb
      - xpack.security.enabled=true
      - xpack.security.authc.api_key.enabled=true
      - ELASTIC_PASSWORD=espass
    ulimits:
      memlock:
        soft: -1
        hard: -1
    healthcheck:
      test: ["CMD", "curl", "-f", "-u", "elastic:espass", "http://localhost:9200/_cluster/health"]
      interval: 5s
      timeout: 10s
      retries: 20
      start_period: 30s
    ports:
      - "19200:9200"
      - "19300:9300"
    volumes:
      - elasticsearch_new_data:/usr/share/elasticsearch/data
      - elasticsearch_new_logs:/usr/share/elasticsearch/logs
      - elasticsearch_new_plugins:/usr/share/elasticsearch/plugins
    networks:
      - agent-network

  agent-kibana:
    container_name: agent-kibana
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/kibana:7.17.25
    restart: always
    depends_on:
      - agent-elasticsearch
    environment:
      ELASTICSEARCH_HOSTS: http://agent-elasticsearch:9200
      ELASTICSEARCH_USERNAME: ${ELASTICSEARCH_USERNAME:-elastic}
      ELASTICSEARCH_PASSWORD: ${ELASTICSEARCH_PASSWORD:-espass}
      I18N_LOCALE: zh-CN
      #SERVER_NAME: kibana.example.com5601
      ELASTICSEARCH_ENABLE_SECURITY: ${ELASTICSEARCH_ENABLE_SECURITY:-false}
    healthcheck:
      test: [ "CMD", "curl", "-f", "-u", "elastic:espass", "http://localhost:5601/api/status" ]
      interval: 5s
      timeout: 10s
      retries: 20
      start_period: 30s
    ports:
      - "5601:5601"
    volumes:
      - kibana_data:/usr/share/kibana/data
    networks:
      - agent-network

  #agent_logstash
  agent-logstash:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/agent_logstash:v3
    container_name: agent-logstash
    volumes:
      - ./config/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    restart: always
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:9600/" ]
      interval: 5s
      timeout: 10s
      retries: 20
      start_period: 20s
    ports:
      - 5499:5499
      - 9600:9600
    networks:
      - agent-network

  # MySQL
  agent-mysql:
    container_name: agent-mysql
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/mysql:8.4.3
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD:-root123}
      MYSQL_DATABASE: ${MYSQL_DATABASE:-agent2_prd}
      MYSQL_USER: ${MYSQL_USER:-agent}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD:-root123}
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u${MYSQL_USER:-agent}", "-p${MYSQL_ROOT_PASSWORD:-root123}"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 10s
    volumes:
      - mysql_data:/bitnami/mysql
      - ./db/my_custom.cnf:/opt/bitnami/mysql/conf/my_custom.cnf:ro
      - ./db/agent.sql:/docker-entrypoint-initdb.d/init.sql  # 挂载 SQL 文件到容器的初始化目录
    networks:
      - agent-network

  # Redis
  agent-redis:
    container_name: agent-redis
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/official-redis:7.4.2
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redispass}
    healthcheck:
      test: ["CMD", "sh", "-c", "redis-cli -a $$REDIS_PASSWORD --no-auth-warning ping | grep PONG"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 10s
    command: redis-server /usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./db/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - agent-network

  # MongoDB
  agent-mongo:
    container_name: agent-mongo
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/mongo:4.4.29
    environment:
      <<: *shared-env
    healthcheck:
      test: ["CMD", "mongo", "--eval", "db.runCommand('ping').ok"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 10s
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - agent-network

  # Minio
  agent-minio:
    container_name: agent-minio
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/minio:2024
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-admin123456}
      MINIO_DEFAULT_BUCKETS: ${MINIO_DEFAULT_BUCKETS:-teamprivkey}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 10s
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/bitnami/minio
      - minio_data_extra:/bitnami/minio/data
      - minio_certs:/certs
    networks:
      - agent-network

  # Frontend
  frontend:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/prompt_frontend:v1.09.dev
    container_name: agent-web-frontend
    restart: always
    ports:
      - "30081:3000"
    healthcheck:
      test: [ "CMD", "wget", "-q", "--spider", "http://localhost:3000" ]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - agent-network
    logging:
      driver: "json-file"

  # Backend
  agent-web-backend:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/prompt_backend:v4
    container_name: agent-web-backend
    restart: always
    depends_on:
      - agent-mysql
      - agent-elasticsearch
      - agent-redis
      - agent-mongo
      - agent-minio
      - agent-logstash
    environment:
      env: ${AGENT_ENV_CONFIG:-private}
      WEB_CONCURRENCY: ${WEB_CONCURRENCY:-2}
      env_channel: ${env_channel:-private}
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost/api/health" ]
      interval: 5s
      timeout: 5s
      retries: 20
      start_period: 30s
    ports:
      - "30082:80"
    volumes:
      - ./config/agent_config.json:/app/config/config.json
      - ./config/agent_platform.json:/app/config/platform.json
      - ./logs/agent-web-backend:/app/logs
    networks:
      - agent-network
    logging:
      driver: "json-file"

  # Backend Beat
  agent-web-backend-beat:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/prompt_backend:v4
    container_name: agent-web-backend-beat
    restart: always
    depends_on:
      - agent-mysql
      - agent-elasticsearch
      - agent-redis
      - agent-mongo
      - agent-minio
      - agent-logstash
    environment:
      env: ${AGENT_ENV_CONFIG:-private}
      start_type: beat
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep '[c]elery.*celery_app beat' > /dev/null || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./config/agent_config.json:/app/config/config.json
      - ./config/agent_platform.json:/app/config/platform.json
      - ./logs/agent-web-backend-beat:/app/logs
    networks:
      - agent-network
    logging:
      driver: "json-file"

  # Backend Worker
  agent-web-backend-worker:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/prompt_backend:v4
    container_name: agent-web-backend-worker
    restart: always
    depends_on:
      - agent-mysql
      - agent-elasticsearch
      - agent-redis
      - agent-mongo
      - agent-minio
      - agent-logstash
    environment:
      env: ${AGENT_ENV_CONFIG:-private}
      start_type: worker
    healthcheck:
      test: ["CMD-SHELL", "ps aux | grep '[c]elery.*celery_app worker' > /dev/null || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./config/agent_config.json:/app/config/config.json
      - ./config/agent_platform.json:/app/config/platform.json
      - ./logs/agent-web-backend-worker:/app/logs
    networks:
      - agent-network
    logging:
      driver: "json-file"

  # Agent Runner
  agentrunner:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/async_agentrunner:v4
    container_name: agentrunner
    restart: always
    depends_on:
      - agent-mysql
      - agent-elasticsearch
      - agent-redis
      - agent-mongo
      - agent-minio
      - agent-logstash
    environment:
      ENV_NAME: dev
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:5001/health" ]
      interval: 5s
      timeout: 10s
      retries: 20
      start_period: 30s
    ports:
      - "5002:5001"
    volumes:
      - ./config/agentrunner:/app/env/.env
      - ./logs/agentrunner:/app/logs
    networks:
      - agent-network

  # Agent Code
  agent_code:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/agentapi_code:v1
    container_name: agent-code-server
    restart: always
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8080/health" ]
      interval: 5s
      timeout: 1s
      retries: 20
      start_period: 20s
    ports:
      - 8080:8080
    networks:
      - agent-network

  #web_crawl
  web_crawl:
    image: registry.cn-hangzhou.aliyuncs.com/yifangyun-library/web_crawl:v3
    container_name: agent-web-crawl
    restart: always
    healthcheck:
      test: [ "CMD", "sh", "-c", "curl -s -o /dev/null -w '%{http_code}' http://localhost/ | grep -E '200|403|404'" ]
      interval: 5s
      timeout: 10s
      retries: 20
      start_period: 20s
    networks:
      - agent-network
    ports:
      - 8081:80



networks:
  agent-network:
    driver: bridge
    driver_opts:
      com.docker.network.driver.mtu: 1450


volumes:
  elasticsearch_data:
    driver: local
  elasticsearch_new_data:
    driver: local
  elasticsearch_new_logs:
    driver: local
  elasticsearch_new_plugins:
    driver: local
  mysql_data:
    driver: local
  redis_data:
    driver: local
  mongo_data:
    driver: local
  minio_data:
    driver: local
  minio_data_extra:
    driver: local
  minio_certs:
    driver: local
  kibana_data:
    driver: local