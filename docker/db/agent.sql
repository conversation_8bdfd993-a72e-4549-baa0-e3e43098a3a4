-- ----------------------------
-- Table structure for application_flow_relation
-- ----------------------------
DROP TABLE IF EXISTS `application_flow_relation`;
CREATE TABLE `application_flow_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `application_id` int NOT NULL DEFAULT '0' COMMENT '应用ID',
  `template_id` int NOT NULL COMMENT '关联的flow_id',
  `source_type` tinyint NOT NULL DEFAULT '1' COMMENT '应用来源 1:agent平台 2:qpass',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


-- ----------------------------
-- Table structure for third_party_platforms
-- ----------------------------
DROP TABLE IF EXISTS `third_party_platforms`;
CREATE TABLE `third_party_platforms` (
  `id` int NOT NULL AUTO_INCREMENT,
  `src` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台来源',
  `api_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT 'api_key',
  `api_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '1' COMMENT 'api_secret',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，1：不删除，0：已删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for action_chain
-- ----------------------------
DROP TABLE IF EXISTS `action_chain`;
CREATE TABLE `action_chain` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '链图表主键',
  `chain_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链图编号',
  `prompt_id` int NOT NULL DEFAULT '-1' COMMENT '提示id',
  `model_id` int NOT NULL DEFAULT '-1' COMMENT '模型id',
  `type` int NOT NULL DEFAULT '1' COMMENT '类型 1：文生文 2：文生图 ',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `desc` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `json_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '链图参数',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id ，0：系统 ',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除 1：不删除  0：删除',
  `model_type` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型类型 文生图 stable_diffusion  文生文：glm/GPT',
  `images` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '图片',
  `deployment_id` int NOT NULL DEFAULT '0' COMMENT '部署Id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `version` tinyint NOT NULL DEFAULT '1' COMMENT '版本序号',
  `publish_time` datetime DEFAULT NULL COMMENT '最后发布时间',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '发布状态: 1 未发布，2 发布',
  `template_check_id` int NOT NULL DEFAULT '0' COMMENT '公开 审核ID',
  `template_check_status` tinyint NOT NULL DEFAULT '-1' COMMENT '公开 审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 4.取消审核',
  `public_time` datetime DEFAULT NULL COMMENT '最后公开时间',
  `star_nums` int NOT NULL DEFAULT '0' COMMENT '引用量',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队ID',
  `is_top` tinyint NOT NULL DEFAULT '2' COMMENT '是否置顶，1.置顶， 2.不置顶',
  `top_time` datetime DEFAULT NULL COMMENT '置顶时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='提示工程表(文生文+文生图)';

-- ----------------------------
-- Table structure for action_chain_api
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_api`;
CREATE TABLE `action_chain_api` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '部署-密钥表主键',
  `app_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'appid',
  `app_secret` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'app_secret',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模版类型 1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent',
  `template_id` int NOT NULL COMMENT '模版ID ',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模版部署密钥表';

-- ----------------------------
-- Table structure for action_chain_flow
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_flow`;
CREATE TABLE `action_chain_flow` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '链图表主键',
  `chain_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链图编号',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `en_title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文标题',
  `desc` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `json_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '链图参数',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除 1：不删除  0：删除',
  `images` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `deployment_id` int NOT NULL DEFAULT '0' COMMENT '部署Id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `input_type` tinyint NOT NULL DEFAULT '1' COMMENT 'flow开始节点类型1、默认变量 2、定时任务触发 3、webhook触发',
  `chain_status` tinyint NOT NULL DEFAULT '1' COMMENT '运行状态 未调试:1， 调试中:2， 调试成功:3 ，调试失败:4 ，已编辑待调试:5',
  `version` tinyint NOT NULL DEFAULT '1' COMMENT '版本序号',
  `star_nums` int NOT NULL DEFAULT '0' COMMENT '引用量',
  `publish_time` datetime DEFAULT NULL COMMENT '最后发布时间',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '发布状态 未发布:1， 发布:2',
  `template_check_id` int NOT NULL DEFAULT '0' COMMENT '公开审核ID',
  `template_check_status` tinyint NOT NULL DEFAULT '-1' COMMENT '公开审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 ',
  `public_time` datetime DEFAULT NULL COMMENT '最后公开时间',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队ID',
  `trigger_code` bigint NOT NULL DEFAULT '0' COMMENT 'DS运行实例ID',
  `flow_type` int NOT NULL DEFAULT '1' COMMENT '1、普通 2、交互',
  `can_be_plugin` tinyint NOT NULL DEFAULT '1' COMMENT '0、不可作为插件 1、可作为插件引用',
  `is_top` tinyint NOT NULL DEFAULT '2' COMMENT '是否置顶，1.置顶， 2.不置顶',
  `top_time` datetime DEFAULT NULL COMMENT '置顶时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能表(flow)';

-- ----------------------------
-- Table structure for action_chain_flow_blocks
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_flow_blocks`;
CREATE TABLE `action_chain_flow_blocks` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL COMMENT 'flow模版ID',
  `block_index` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块索引',
  `block_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块配置',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `branch_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分支ID，主分支为“”',
  `block_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块类型',
  `loop_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '循环节点索引',
  PRIMARY KEY (`id`),
  KEY `idx_temp_id` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能节点表(flow block)';

-- ----------------------------
-- Table structure for action_chain_flow_test_blocks_log
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_flow_test_blocks_log`;
CREATE TABLE `action_chain_flow_test_blocks_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL COMMENT 'flow模版ID',
  `block_index` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块索引',
  `test_log_id` int NOT NULL COMMENT 'flow运行测试日志ID',
  `block_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块配置',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `branch_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分支ID，主分支为“”',
  `block_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块类型',
  PRIMARY KEY (`id`),
  KEY `index_tbtest_id` (`template_id`,`block_index`,`test_log_id`),
  KEY `idx_action_chain_flow_blocks_log_test_log_id` (`test_log_id`,`block_index`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能发布后节点测试记录表(flow block test run)';

-- ----------------------------
-- Table structure for action_chain_flow_test_log
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_flow_test_log`;
CREATE TABLE `action_chain_flow_test_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id ',
  `json_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '链图参数',
  `chain_status` tinyint NOT NULL DEFAULT '1' COMMENT '运行状态 未调试:1， 调试中:2， 调试成功:3 ，调试失败:4 ，已编辑待调试:5',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '回调ID',
  `source` tinyint NOT NULL DEFAULT '1' COMMENT '1:agent调用 2:团队发布flow后调用 3:发现市场中调用',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `trigger_code` bigint NOT NULL DEFAULT '0' COMMENT 'DS运行实例ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能发布后测试记录表(flow test run)';

-- ----------------------------
-- Table structure for action_chain_flow_version
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_flow_version`;
CREATE TABLE `action_chain_flow_version` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '版本表主键',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本序号',
  `serial_number` int NOT NULL DEFAULT '0' COMMENT '序列号',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `chain_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '配置',
  `input_type` tinyint NOT NULL DEFAULT '1' COMMENT 'flow开始节点类型1、默认变量 2、定时任务触发 3、webhook触发',
  `generate_time` datetime DEFAULT NULL COMMENT '生成版本时间',
  `publish_time` datetime DEFAULT NULL COMMENT '最后发布时间',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '发布状态 未发布:1， 发布:2',
  `version_remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本备注',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_template_version` (`template_id`,`version`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='flow版本表';

-- ----------------------------
-- Table structure for action_flow_ds
-- ----------------------------
DROP TABLE IF EXISTS `action_flow_ds`;
CREATE TABLE `action_flow_ds` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL DEFAULT '0' COMMENT 'template_id取值 action_chain_flow 表中的 ID\n',
  `flow_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'DS工作流code',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='技能(flow)和DS工作流对应关系表';

-- ----------------------------
-- Table structure for action_flow_version_ds
-- ----------------------------
DROP TABLE IF EXISTS `action_flow_version_ds`;
CREATE TABLE `action_flow_version_ds` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL DEFAULT '0' COMMENT '模版ID',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本序号',
  `flow_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `env` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '所属环境',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uniq_template_version` (`template_id`,`version`,`env`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='flow版本ds_id对照表';

-- ----------------------------
-- Table structure for admin_member_role
-- ----------------------------
DROP TABLE IF EXISTS `admin_member_role`;
CREATE TABLE `admin_member_role` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
  `role_id` int NOT NULL DEFAULT '2' COMMENT '团队角色 1:系统超级管理员 2:系统管理员',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除 1:未删除 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统-用户-角色关系表';

-- ----------------------------
-- Table structure for agent
-- ----------------------------
DROP TABLE IF EXISTS `agent`;
CREATE TABLE `agent` (
    `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体名称',
  `description` varchar(1000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体描述详情',
  `icon` varchar(300) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体图标s3地址',
  `llm_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '360gpt-pro' COMMENT '使用的大模型类型(1.360gpt_pro 2. gpt3.5 3. gpt4)',
  `llm_provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'zhinao' COMMENT '模型供应商',
  `llm_param` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '大模型参数',
  `audio_input_status` tinyint NOT NULL DEFAULT '2' COMMENT '语音输入功能，1:开启，2:不开启，默认不开启',
  `voice_type` tinyint NOT NULL DEFAULT '1' COMMENT '输入声音类型\nZH_CN_XiaohanNeural = 1\nZH_CN_XiaoqiuNeural = 2\nZH_CN_XiaoxiaoMultilingualNeural3 = 3\nZH_CN_XiaoxiaoNeural = 4\nZH_CN_YunjianNeural = 5\nZH_CN_YunyangNeural = 6',
  `audio_status` tinyint NOT NULL DEFAULT '1' COMMENT '语音功能，1:开启，2:不开启，默认开启',
  `digit_image_status` tinyint NOT NULL DEFAULT '2' COMMENT '数字形象开关 1:开启，2:不开启，默认不开启',
  `digit_avatar_status` tinyint NOT NULL DEFAULT '2' COMMENT '数字形象是否充当头像开关，1:开启，2:不开启，默认不开启',
  `digit_avatar_url` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '数字形象url',
  `audio_automatic_play` tinyint NOT NULL DEFAULT '2' COMMENT '语音自动播放，1:开启，2:不开启，默认不开启',
  `openapi_status` tinyint NOT NULL DEFAULT '1' COMMENT '是否开启openapi,1是开启，2是关闭，默认是2',
  `openapi_admin_status` tinyint NOT NULL DEFAULT '1' COMMENT '管理台是否开启openapi,1是开启，2是关闭，默认是1',
  `iframe_admin_status` tinyint NOT NULL DEFAULT '1' COMMENT '管理台是否开启Iframe,1是开启，2是关闭，默认是1',
  `digit_avatar_background` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '数字形象背景',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色&提示词',
  `greeting_message` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开场白信息',
  `greeting_question` varchar(3000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开场问题，以逗号隔开',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
  `public_user_id` int NOT NULL DEFAULT '0' COMMENT '最后一次版本提交用户id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '所属项目',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '是否发布1未发布2发布',
  `public_time` datetime DEFAULT NULL COMMENT '公开时间',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `is_suggestion` tinyint NOT NULL DEFAULT '1' COMMENT '是否开启自动建议1关闭2默认3自定义',
  `suggestion` varchar(300) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '建议内容',
  `fork_nums` int NOT NULL DEFAULT '0' COMMENT '复制次数',
  `template_check_status` tinyint NOT NULL DEFAULT '-1' COMMENT '公开 审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 4.取消审核\n',
  `template_check_id` int NOT NULL DEFAULT '0' COMMENT '审核关联ID',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除0是删除1正常',
  `is_privacy` tinyint NOT NULL DEFAULT '1' COMMENT '是否公开配置，1-不公开，2-公开',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL,
  `strategy` int NOT NULL DEFAULT '2' COMMENT 'agent的执行策略1 function calling 2.rewoo 3. react',
  `memory_status` int NOT NULL DEFAULT '3' COMMENT '是否开启记忆，1开启基础记忆，2开启基础记忆和图记忆，3关闭，默认是3',
  `memory_setting` varchar(300) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '记忆个性化设置',
  `llm_icon` varchar(500) COLLATE utf8mb4_general_ci NULL COMMENT '大模型图标',
  `llm_id` varchar(100) COLLATE utf8mb4_general_ci NULL COMMENT '大模型id',
  `is_top` tinyint NOT NULL DEFAULT '2' COMMENT '是否置顶，1.置顶， 2.不置顶',
  `top_time` datetime DEFAULT NULL COMMENT '置顶时间',
    PRIMARY KEY (`id`),
  KEY `idx_id_fork_nums` (`id`,`fork_nums`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)基础信息表';

-- ----------------------------
-- Table structure for agent_access
-- ----------------------------
DROP TABLE IF EXISTS `agent_access`;
CREATE TABLE `agent_access` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT 'agent调用表主键',
  `agent_id` int NOT NULL COMMENT 'agentID',
  `last_called_time` int NOT NULL DEFAULT '0' COMMENT '最后一次调用时间',
  `call_status` tinyint NOT NULL DEFAULT '1' COMMENT '最后一次调用状态 1:成功 2:失败 3：中止',
  `call_count` int NOT NULL DEFAULT '0' COMMENT '调用量',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_agent_id` (`agent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='agent调用表';

-- ----------------------------
-- Table structure for agent_api_auth
-- ----------------------------
DROP TABLE IF EXISTS `agent_api_auth`;
CREATE TABLE `agent_api_auth` (
  `id` int NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL DEFAULT '0' COMMENT '智能体id',
  `channel_id` int NOT NULL DEFAULT '0' COMMENT '渠道id',
  `src` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源',
  `url_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '访问地址',
  `app_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '秘钥',
  `used_status` tinyint NOT NULL DEFAULT '2' COMMENT '2：正常 1:关闭',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `channel_identifier` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道标识',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for agent_channel
-- ----------------------------
DROP TABLE IF EXISTS `agent_channel`;
CREATE TABLE `agent_channel` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `agent_history_id` int NOT NULL DEFAULT '0' COMMENT 'agent发布历史id',
  `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `agent_id` int NOT NULL DEFAULT '0' COMMENT '关联agentid',
  `channel_id` int NOT NULL DEFAULT '0' COMMENT '关联渠道id',
  `channel_tag_id` int NOT NULL DEFAULT '-1' COMMENT '默认值：未配置分类',
  `channel_tag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道标签名称',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1-未删除，2-删除',
  `is_last` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否是最后审核0-否1-是',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `template_check_id` int NOT NULL DEFAULT '0' COMMENT '关联审批表id',
  `template_check_status` int NOT NULL DEFAULT '-1' COMMENT '公开 审核状态 -2: 纳米用户初创建初始状态 -1:未审核 1:待审核 2:通过 3:拒绝 4.取消审核\n',
  `weight` int DEFAULT '1' COMMENT 'agent展示权重,越大越靠前',
  `is_hot` tinyint NOT NULL DEFAULT '0' COMMENT '是否在该渠道下是热门',
  `fork_nums` int NOT NULL DEFAULT '0' COMMENT '复制次数',
  `agent_created_time` datetime DEFAULT NULL COMMENT '创建时间',
  `channel_unique_flag` tinyint NOT NULL DEFAULT '0' COMMENT '渠道唯一标识符 1：唯一标识状态',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建用户名称',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '创建用户id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '所属项目',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_agent_channel_tag_version` (`version`,`agent_id`,`channel_id`,`channel_tag_id`),
  KEY `idx_agent_id_is_deleted` (`agent_id`,`is_deleted`),
  KEY `idx_channel_id_is_deleted` (`channel_id`,`is_deleted`),
  KEY `idx_version` (`version`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for agent_chat_message
-- ----------------------------
DROP TABLE IF EXISTS `agent_chat_message`;
CREATE TABLE `agent_chat_message` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '会话ID',
  `message_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '聊天信息唯一ID',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '聊天内容',
  `source` tinyint NOT NULL DEFAULT '1' COMMENT '聊天来源1是系统2是用户提问3智能体回答',
  `trigger_type` tinyint NOT NULL DEFAULT '0' COMMENT '触发类型0未触发，1是flow,2是api,3是知识库',
  `user_id` int DEFAULT NULL COMMENT '如果是用户提问，冗余字段',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)聊天信息表';

-- ----------------------------
-- Table structure for agent_chat_session
-- ----------------------------
DROP TABLE IF EXISTS `agent_chat_session`;
CREATE TABLE `agent_chat_session` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL COMMENT '智能体ID',
  `session_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '会话唯一ID',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '创建会话的人',
  `external_user` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '外部用户信息',
  `source_id` int NOT NULL DEFAULT '0' COMMENT '关联鉴权信息的ID',
  `start_time` datetime DEFAULT NULL COMMENT '会话开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '会话结束时间',
  `current_agent_status` tinyint NOT NULL DEFAULT '1' COMMENT '当前agent的状态1发布前2发布后',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `channel` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道标识',
  `version` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_session_id` (`session_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)聊天记录会话表';

-- ----------------------------
-- Table structure for agent_flow_relation
-- ----------------------------
DROP TABLE IF EXISTS `agent_flow_relation`;
CREATE TABLE `agent_flow_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int DEFAULT NULL COMMENT '智能体ID',
  `template_id` int NOT NULL COMMENT '关联的flow_id',
  `trigger_word` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '触发词',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)和技能(flow)关联关系表';

-- ----------------------------
-- Table structure for agent_history
-- ----------------------------
DROP TABLE IF EXISTS `agent_history`;
CREATE TABLE `agent_history` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL DEFAULT '0' COMMENT '智能体id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体名称',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体描述详情',
  `llm_model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '使用的大模型类型',
  `llm_provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模型供应商',
  `llm_param` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '大模型参数',
  `llm_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '大模型图标',
  `llm_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '大模型id',
  `greeting_question` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '开场问题',
  `greeting_message` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '开场白信息',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '智能体图标s3地址',
  `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '角色&提示词',
  `publish_date` date NOT NULL DEFAULT '1970-01-01' COMMENT '发布日期',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '发布人id',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '项目id',
  `team_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '项目名称',
  `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布版本',
  `object_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '关联mongo_id',
  `template_check_id` int NOT NULL DEFAULT '0' COMMENT '关联审批表id',
  `template_check_status` int NOT NULL DEFAULT '-1' COMMENT '公开 审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 4.取消审核\n',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `version_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '版本描述',
  `weight` int DEFAULT '0' NULL COMMENT 'agent展示权重,越大越靠前',
  `is_hot` TINYINT(3) NOT NULL DEFAULT '0' COMMENT '是否在该渠道下是热门',
  PRIMARY KEY (`id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_template_check_status` (`template_check_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for agent_knowledge_relation
-- ----------------------------
DROP TABLE IF EXISTS `agent_knowledge_relation`;
CREATE TABLE `agent_knowledge_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int DEFAULT NULL COMMENT '智能体ID',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '知识库图标',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '知识库名称',
  `uuid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'uuid 和agent_id保持一致',
  `template_id` int NOT NULL COMMENT '关联的知识库ID',
  `trigger_word` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '触发词',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `type` TINYINT(3) NOT NULL DEFAULT '1' COMMENT '类型 1为研究院知识库 2为云盘知识库',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)和知识库(knowledge)关联关系表';

-- ----------------------------
-- Table structure for agent_op_log
-- ----------------------------
DROP TABLE IF EXISTS `agent_op_log`;
CREATE TABLE `agent_op_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `operator_id` int DEFAULT NULL COMMENT '操作人ID',
  `operator_type` int DEFAULT NULL COMMENT '操作类型1是添加2是修改是3删除4发布5取消发布',
  `before_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '处理前的内容',
  `after_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '处理后的内容',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)操作记录表';

-- ----------------------------
-- Table structure for agent_plugin_relation
-- ----------------------------
DROP TABLE IF EXISTS `agent_plugin_relation`;
CREATE TABLE `agent_plugin_relation` (
   `id` int unsigned NOT NULL AUTO_INCREMENT,
  `agent_id` int NOT NULL DEFAULT '0' COMMENT 'Agent_id',
  `api_id` int NOT NULL DEFAULT '0' COMMENT 'api_api表中id',
  `api_version` int NOT NULL DEFAULT '0' COMMENT 'api版本号',
  `template_id` int NOT NULL DEFAULT '0' COMMENT 'api_id 仅作记录',
  `auth_id` int NOT NULL DEFAULT '0' COMMENT 'api_auth表中id',
  `api_name` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api的名称',
  `api_ename` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api英文名称',
  `api_desc` varchar(3000) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'API的描述',
  `url` text COLLATE utf8mb4_general_ci NOT NULL COMMENT 'API地址',
  `method` varchar(30) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求的方法',
  `querystring` text COLLATE utf8mb4_general_ci COMMENT '请求的参数',
  `headers` text COLLATE utf8mb4_general_ci COMMENT '请求的header',
  `body` text COLLATE utf8mb4_general_ci COMMENT '请求的body体',
  `body_type` varchar(20) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求的类型是json还是formdata',
  `response_filter` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '请求返回的过滤结果规则',
  `trigger_word` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '触发词',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除1是正常0是删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `icon` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件图标',
  PRIMARY KEY (`id`),
  KEY `idx_agent_id` (`agent_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_api_id` (`api_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='智能体(agent)和插件(api)关联关系表';

-- ----------------------------
-- Table structure for api_api
-- ----------------------------
DROP TABLE IF EXISTS `api_api`;
CREATE TABLE `api_api` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` int unsigned NOT NULL DEFAULT '0',
  `user_id` int unsigned NOT NULL DEFAULT '0',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `images` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `detail` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `domain` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求域名',
  `star_nums` int unsigned NOT NULL DEFAULT '0',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '发布状态，默认为 1:未发布状态，2：已发布',
  `publish_time` datetime DEFAULT NULL COMMENT '最后发布时间',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，0:删除 1:未删除',
  `auth_type` tinyint NOT NULL DEFAULT '1' COMMENT '授权方式1-不需要,2-appid+secret,3-oauth2',
  `is_auth` tinyint NOT NULL DEFAULT '2' COMMENT '是否需要授权1-需要2-不需要',
  `template_check_id` int unsigned NOT NULL DEFAULT '0' COMMENT '审核ID',
  `template_check_status` tinyint NOT NULL DEFAULT '-1' COMMENT '审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 4:撤销',
  `public_time` datetime DEFAULT NULL COMMENT '公开时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `api_type` tinyint NOT NULL DEFAULT '1' COMMENT '1-默认是url，2-代码',
  `is_top` tinyint NOT NULL DEFAULT '2' COMMENT '是否置顶，1.置顶， 2.不置顶',
  `top_time` datetime DEFAULT NULL COMMENT '置顶时间',
  PRIMARY KEY (`id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_check_status` (`template_check_status`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for api_api_relation
-- ----------------------------
DROP TABLE IF EXISTS `api_api_relation`;
CREATE TABLE `api_api_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `api_id` int NOT NULL DEFAULT '0' COMMENT '对应api_api中id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户ID',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '项目ID',
  `template_id` int NOT NULL DEFAULT '0' COMMENT '对应api_template中ID',
  `auth_id` int NOT NULL DEFAULT '0' COMMENT '对应api_auth中ID',
  `relation_id` int NOT NULL DEFAULT '0' COMMENT 'AgentID或者FlowID',
  `relation_type` tinyint NOT NULL DEFAULT '5' COMMENT '3:Flow  6:Agent',
  `relation_ext` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '扩展信息',
  PRIMARY KEY (`id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_relation_id` (`relation_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='插件关联关系表';

DROP TABLE IF EXISTS `api_api_version`;
CREATE TABLE `api_api_version` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `api_id` int unsigned NOT NULL DEFAULT '0' COMMENT '插件id',
  `team_id` int unsigned DEFAULT '0' COMMENT '团队id',
  `api_type` tinyint NOT NULL DEFAULT '1' COMMENT '1-默认是url，2-代码',
  `api_version_str` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `images` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `detail` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `domain` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求域名',
  `publish_user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '发布人ID',
  `publish_user_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布人',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '发布状态，默认为 1:未发布2:已发布',
  `publish_version` int unsigned NOT NULL DEFAULT '1' COMMENT '发布版本号',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `publish_detail` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '版本描述',
  `publish_last` tinyint NOT NULL DEFAULT '0' COMMENT '是否是最后发布版本0:不是1:是',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，0:删除 1:未删除',
  `template_check_id` int unsigned NOT NULL DEFAULT '0' COMMENT '审核ID',
  `template_check_status` tinyint NOT NULL DEFAULT '-1' COMMENT '审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 4:撤销',
  `public_status` tinyint NOT NULL DEFAULT '1' COMMENT '公开状态 1:未公开2:已公开',
  `public_time` datetime DEFAULT NULL COMMENT '公开时间',
  `public_last` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否是最后公开0:不是1:是',
  `expiration` int unsigned NOT NULL DEFAULT '0' COMMENT '插件过期时间(取消公开时)',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_api_id` (`api_id`,`publish_version`) USING BTREE,
  KEY `idx_public_status` (`public_status`) USING BTREE,
  KEY `idx_team_id` (`team_id`),
  KEY `idx_user_id` (`publish_user_name`),
  KEY `idx_api_version` (`api_version_str`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='插件版本表';
-- ----------------------------
-- Table structure for api_auth
-- ----------------------------
DROP TABLE IF EXISTS `api_auth`;
CREATE TABLE `api_auth` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `api_id` int unsigned NOT NULL DEFAULT '0' COMMENT '对应api_api表中id',
  `team_id` int unsigned NOT NULL DEFAULT '0',
  `user_id` int unsigned NOT NULL DEFAULT '0',
  `auth_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `auth_info` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '授权信息',
  `is_default` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1-创建时，2-引用时',
  `is_deleted` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否删除，0:删除 1:未删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_team_id` (`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for api_template
-- ----------------------------
DROP TABLE IF EXISTS `api_template`;
CREATE TABLE `api_template` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '创建人',
  `api_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'api_api表中id',
  `images` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `api_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api名称',
  `api_ename` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `api_desc` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'API描述',
  `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'api请求url',
  `url_path` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'urlPATH地址',
  `method` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求方法',
  `querystring` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'query参数',
  `headers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'header参数',
  `body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'body参数',
  `body_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'json' COMMENT 'body类型',
  `response_filter` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '返回结果配置过滤',
  `test_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0-测试未通过1-测试通过',
  `test_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '测试结果',
  `filter_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '测试结果过滤',
  `publish_status` int NOT NULL DEFAULT '1' COMMENT '发布状态，默认为 1:未发布状态，2：已发布',
  `template_check_id` int NOT NULL DEFAULT '0' COMMENT '审核ID',
  `template_check_status` tinyint NOT NULL DEFAULT '-1' COMMENT '审核状态 -1:未审核 1:待审核 2:通过 3:拒绝 4:撤销',
  `publish_time` datetime DEFAULT NULL COMMENT '最后发布时间',
  `public_time` datetime DEFAULT NULL COMMENT '公开时间',
  `star_nums` int NOT NULL DEFAULT '0' COMMENT '引用量',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除，0:删除 1:未删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `api_type` tinyint NOT NULL DEFAULT '1' COMMENT '1-默认是url，2-代码',
  `api_code` longtext COLLATE utf8mb4_general_ci COMMENT '代码',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_api_id` (`api_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='插件(api)模版表';

-- ----------------------------
-- Table structure for api_template
-- ----------------------------
DROP TABLE IF EXISTS `api_template_version`;
CREATE TABLE `api_template_version` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `api_id` int unsigned NOT NULL DEFAULT '0' COMMENT 'api_api表中id',
  `api_template_id` int NOT NULL DEFAULT '0' COMMENT 'api_template表中的id',
  `api_version_str` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '插件ID和版本号的组合',
  `api_template_version_str` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '动作ID和版本号的组合',
  `api_type` tinyint NOT NULL DEFAULT '1' COMMENT '1-默认是url，2-代码',
  `api_code` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '代码',
  `api_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'api名称',
  `api_ename` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `api_desc` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'API描述',
  `url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT 'api请求url',
  `url_path` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT 'urlPATH地址',
  `method` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '请求方法',
  `querystring` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'query参数',
  `headers` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'header参数',
  `body` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'body参数',
  `body_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'json' COMMENT 'body类型',
  `response_filter` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '返回结果配置过滤',
  `test_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '0-测试未通过1-测试通过',
  `test_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '测试结果',
  `filter_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '测试结果过滤',
  `publish_version` int unsigned NOT NULL DEFAULT '1' COMMENT '发布版本号',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除，0:删除 1:未删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_api_id` (`api_id`),
  KEY `idx_template_id` (`api_template_id`),
  KEY `idx_api_version` (`api_version_str`),
  KEY `idx_template_version` (`api_template_version_str`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
-- ----------------------------
-- Table structure for api_test_log
-- ----------------------------
DROP TABLE IF EXISTS `api_test_log`;
CREATE TABLE `api_test_log` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL COMMENT 'api模版ID',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id',
  `test_log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '运行测试日志ID',
  `debug_type` int NOT NULL DEFAULT '1' COMMENT '测试类型，1: 发布后测试， 2: flow中引用测试， 3:  发现中测试',
  `api_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'api配置',
  `test_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'api测试结果',
  `filter_result` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'api结果过滤返回',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='插件(api)模版测试记录表';

-- ----------------------------
-- Table structure for api_trigger_device
-- ----------------------------
DROP TABLE IF EXISTS `api_trigger_device`;
CREATE TABLE `api_trigger_device` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `api_id` int NOT NULL COMMENT 'api_api中id',
  `api_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `api_ename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '英文名称',
  `api_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `trigger_method` tinyint NOT NULL DEFAULT '1' COMMENT '1-推送，2-拉取',
  `push_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT 'webhook地址',
  `push_params_status` tinyint NOT NULL DEFAULT '1' COMMENT '推送参数，1-未开启，2-开启',
  `request_method` tinyint NOT NULL DEFAULT '1' COMMENT '1-get,2-post',
  `push_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '推送参数',
  `response_status` tinyint NOT NULL DEFAULT '1' COMMENT '1-未开启， 2-开启',
  `response_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '响应配置',
  `test_status` tinyint NOT NULL DEFAULT '1' COMMENT '1-无测试，2-测试通过',
  `test_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '测试结果',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1-未删除，2-删除, 3-中间状态',
  `publish_status` tinyint NOT NULL DEFAULT '1' COMMENT '发布状态，默认为 1:未发布状态，2：已发布',
  `publish_time` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `template_check_id` int DEFAULT '0',
  `template_check_status` tinyint DEFAULT '-1',
  `public_time` datetime DEFAULT NULL,
  `team_id` int DEFAULT '0' COMMENT 'time_id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_id_name_ename` (`api_id`,`api_name`,`api_ename`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='连接器触发动作';

-- ----------------------------
-- Table structure for api_white_list
-- ----------------------------
DROP TABLE IF EXISTS `api_white_list`;
CREATE TABLE `api_white_list` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `domain_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '域名/ip',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1:启用 0:不启用',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1:正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='插件(api)域名白名单表';

-- ----------------------------
-- Table structure for app_white_list
-- ----------------------------
DROP TABLE IF EXISTS `app_white_list`;
CREATE TABLE `app_white_list` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `domain_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '域名/ip',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1:启用 0:不启用',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1:正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for card
-- ----------------------------
DROP TABLE IF EXISTS `card`;
CREATE TABLE `card` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '链图表主键',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '标题',
  `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '英文标题',
  `data_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '链图参数',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除 1：不删除  0：删除',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图片',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='链图表';

-- ----------------------------
-- Table structure for casbin_rule
-- ----------------------------
DROP TABLE IF EXISTS `casbin_rule`;
CREATE TABLE `casbin_rule` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `ptype` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '类型，p：policy， g：用户组',
  `v0` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '角色sub',
  `v1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '域dom',
  `v2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '路由 obj',
  `v3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '方法act',
  `v4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  `v5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='casbin权限表';

-- ----------------------------
-- Table structure for chain_generation
-- ----------------------------
DROP TABLE IF EXISTS `chain_generation`;
CREATE TABLE `chain_generation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '生成表主键',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id',
  `chain_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '链图编号',
  `is_sensitive` int NOT NULL DEFAULT '2' COMMENT '是否包含敏感信息 1: 敏感 2:不敏感 默认：2',
  `input_variable` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '输入参数',
  `json_desc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '链图参数',
  `output` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '输出结果',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='提示工程生成表';

-- ----------------------------
-- Table structure for chain_service_access
-- ----------------------------
DROP TABLE IF EXISTS `chain_service_access`;
CREATE TABLE `chain_service_access` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模版类型 1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '访问唯一id',
  `deploy_id` int NOT NULL DEFAULT '0' COMMENT '部署ID',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
  `chain_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '配置',
  `inputs` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '入参',
  `result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '结果。正常结果和异常结果。 未知异常无结果',
  `call_total` int NOT NULL DEFAULT '0' COMMENT '调用次数',
  `call_success` int NOT NULL DEFAULT '0' COMMENT '成功次数',
  `call_error` int NOT NULL DEFAULT '0' COMMENT '失败次数',
  `call_cancel` int NOT NULL DEFAULT '0' COMMENT '取消次数',
  `call_exception` int NOT NULL DEFAULT '0' COMMENT '异常次数',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `trigger_type` tinyint NOT NULL DEFAULT '0' COMMENT '触发类型 0、default 1、api 2、cron 3、webhook',
  `trigger_code` bigint NOT NULL DEFAULT '0' COMMENT 'DS运行实例ID',
  `flow_type` tinyint NOT NULL DEFAULT '1' COMMENT '1、普通 2、交互',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_log_id` (`log_id`),
  KEY `index_tt` (`template_type`,`end_time`),
  KEY `idx_template_id_type` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部署服务访问记录表';

-- ----------------------------
-- Table structure for chain_service_access_flow_blocks
-- ----------------------------
DROP TABLE IF EXISTS `chain_service_access_flow_blocks`;
CREATE TABLE `chain_service_access_flow_blocks` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL COMMENT 'flow模版ID',
  `block_index` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块索引',
  `block_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块配置',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `branch_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分支ID，主分支为“”',
  `block_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块类型',
  `chain_service_access_id` int NOT NULL DEFAULT '0' COMMENT '部署接口访问日志',
  PRIMARY KEY (`id`),
  KEY `index_template_id_access_id` (`template_id`,`chain_service_access_id`,`block_index`) USING BTREE,
  KEY `idx_chain_service_access_id` (`chain_service_access_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='部署服务技能(flow)节点(block)访问记录表';

-- ----------------------------
-- Table structure for channel_auth
-- ----------------------------
DROP TABLE IF EXISTS `channel_auth`;
CREATE TABLE `channel_auth` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int unsigned NOT NULL DEFAULT '0',
  `team_id` int unsigned NOT NULL DEFAULT '0',
  `channel_id` int unsigned NOT NULL DEFAULT '0' COMMENT '渠道ID',
  `agent_id` int unsigned NOT NULL DEFAULT '0',
  `appid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `secret` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `token` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '扩展字段',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1-未删除2-已删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `qrcode_url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '公众号二维码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_agent_channel` (`agent_id`,`channel_id`),
  KEY `idx_appid` (`appid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for channel_tags
-- ----------------------------
DROP TABLE IF EXISTS `channel_tags`;
CREATE TABLE `channel_tags` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `channel_id` int NOT NULL COMMENT '渠道id',
  `tag_parent_id` int NOT NULL DEFAULT '0' COMMENT '父标签id，如果没有父标签默认为0',
  `tag_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '标签名称',
  `icon` varchar(300) collate utf8mb4_general_ci null comment '标签图标',
  `business_id` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '业务线分类ID',
  `sort` int NOT NULL DEFAULT '0' COMMENT '排序',
  `level` tinyint NOT NULL DEFAULT '1' COMMENT '分类等级',
  `used_status` tinyint NOT NULL DEFAULT '1' COMMENT '启用状态1:开启，2:不开启，默认开启',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `allow_team_id` INT(10) NULL DEFAULT NULL COMMENT '允许团队id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_tag_name` (`channel_id`,`tag_parent_id`,`tag_name`, `business_id`) USING BTREE COMMENT '渠道下各级标签不可重复'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='发布渠道标签表';

-- ----------------------------
-- Table structure for channels
-- ----------------------------
DROP TABLE IF EXISTS `channels`;
CREATE TABLE `channels` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `channel_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '渠道名称',
  `identifier` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道标识',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '渠道描述',
  `used_status` tinyint NOT NULL DEFAULT '1' COMMENT '启用状态1:开启，2:不开启，默认开启',
  `internal_net` tinyint NOT NULL DEFAULT '1' COMMENT '是否是内网1-内网2-外网',
  `agent_relation_nums` int DEFAULT '0' COMMENT '关联的渠道数量-默认为0',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL,
  `sort` tinyint NOT NULL DEFAULT '1' COMMENT '排序权重',
  `image_url` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道图片',
  `show_all` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '是否全平台展示1-是2-只在当前平台显示',
  `src` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'all' COMMENT '渠道来源',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_channel_name` (`channel_name`) USING BTREE COMMENT '渠道名称唯一',
  KEY `idx_identifier` (`identifier`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='agent发布渠道表';

-- ----------------------------
-- Table structure for deployment
-- ----------------------------
DROP TABLE IF EXISTS `deployment`;
CREATE TABLE `deployment` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '部署表主键',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '部署名',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模版类型 1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `execution_engine` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'ds' COMMENT '执行引擎',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '部署状态 1:部署成功 2:已停用',
  `chain_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '配置',
  `last_op_uid` int NOT NULL DEFAULT '0' COMMENT '最后操作人',
  `last_called_time` int NOT NULL DEFAULT '0' COMMENT '最后调用时间',
  `call_status` tinyint NOT NULL DEFAULT '0' COMMENT '调用状态 1:成功 2:失败 3:取消 4:异常 0:未调用',
  `call_count` int NOT NULL DEFAULT '0' COMMENT '调用量',
  `remark` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `deploy_time` datetime DEFAULT NULL COMMENT '部署时间',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模版部署表';

-- ----------------------------
-- Table structure for deployment_cron_flow
-- ----------------------------
DROP TABLE IF EXISTS `deployment_cron_flow`;
CREATE TABLE `deployment_cron_flow` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '部署表主键',
  `cron` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '触发周期',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `deploy_id` int NOT NULL COMMENT '部署ID ',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='部署定时flow表';

-- ----------------------------
-- Table structure for deployment_webhook_flow
-- ----------------------------
DROP TABLE IF EXISTS `deployment_webhook_flow`;
CREATE TABLE `deployment_webhook_flow` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '部署表主键',
  `event_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '事件名称',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `deploy_id` int NOT NULL COMMENT '部署ID ',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='部署wh触发flow表';

-- ----------------------------
-- Table structure for external_team_relation
-- ----------------------------
DROP TABLE IF EXISTS `external_team_relation`;
CREATE TABLE `external_team_relation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL COMMENT '团队ID',
  `external_team_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '外部项目id',
  `src` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '来源',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，1: 否， 0: 是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `external_team_relation_pk` (`team_id`),
  KEY `external_team_relation_external_team_id_index` (`external_team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='外部项目团队关系映射表';

-- ----------------------------
-- Table structure for feedback
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '申请人ID',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '反馈标题',
  `contact_information` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '联系信息',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime DEFAULT NULL,
  `is_deleted` tinyint NOT NULL DEFAULT '1',
  `src` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'agent' COMMENT '用户来源',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户反馈表';

-- ----------------------------
-- Table structure for flow_access_count
-- ----------------------------
DROP TABLE IF EXISTS `flow_access_count`;
CREATE TABLE `flow_access_count` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL DEFAULT '0',
  `template_name` varchar(255) NOT NULL DEFAULT '',
  `date` varchar(255) NOT NULL DEFAULT '',
  `call_count` int NOT NULL DEFAULT '0',
  `team_id` int NOT NULL DEFAULT '0',
  `team_name` varchar(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for flow_block_plugin_relation
-- ----------------------------
DROP TABLE IF EXISTS `flow_block_plugin_relation`;
CREATE TABLE `flow_block_plugin_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL COMMENT '模版ID ',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'default' COMMENT '版本序号',
  `block_index` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块索引',
  `flow_plugin_id` int NOT NULL COMMENT '关联的flow组件id',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_template_id_index` (`template_id`,`block_index`,`version`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='flow插件关联关系表';

-- ----------------------------
-- Table structure for flow_cache_record
-- ----------------------------
DROP TABLE IF EXISTS `flow_cache_record`;
CREATE TABLE `flow_cache_record` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '部署表主键',
  `redis_key` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'key',
  `cache_data` int NOT NULL COMMENT '缓存数据',
  `version` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `start_time` int NOT NULL COMMENT '创建时间 ',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_redis_key` (`redis_key`) USING BTREE,
  KEY `idx_template_id` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='flow缓存记录表';

-- ----------------------------
-- Table structure for flow_plugin_access
-- ----------------------------
DROP TABLE IF EXISTS `flow_plugin_access`;
CREATE TABLE `flow_plugin_access` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '版本号',
  `log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '访问唯一id',
  `trace_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '主流程log_id',
  `p_template_id` int NOT NULL DEFAULT '0' COMMENT '主流程template_id',
  `p_block_index` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '主流程节点id',
  `source_type` int NOT NULL DEFAULT '1' COMMENT '子流程触发类型 1、api 2、非api',
  `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
  `chain_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '配置',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '运行状态 初始化:1， 运行中:2， 成功:3 ，失败:4 ，异常:5',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `trigger_code` bigint NOT NULL DEFAULT '0' COMMENT 'DS运行实例ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_log_id` (`log_id`) USING BTREE,
  KEY `idx_template_id_type` (`template_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='flow插件访问日志';

-- ----------------------------
-- Table structure for flow_plugin_access_flow_blocks
-- ----------------------------
DROP TABLE IF EXISTS `flow_plugin_access_flow_blocks`;
CREATE TABLE `flow_plugin_access_flow_blocks` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL COMMENT '子流程运行节点表ID',
  `block_index` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模块索引',
  `block_config` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块配置',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `branch_id` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '分支ID，主分支为“”',
  `block_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块类型',
  `access_id` int NOT NULL DEFAULT '0' COMMENT '部署接口访问日志',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_template_id_access_id_block_index` (`template_id`,`block_index`,`access_id`) USING BTREE,
  KEY `idx_access_id` (`access_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci ROW_FORMAT=DYNAMIC COMMENT='子流程运行节点表';

-- ----------------------------
-- Table structure for gui_flow_white_list
-- ----------------------------
DROP TABLE IF EXISTS `gui_flow_white_list`;
CREATE TABLE `gui_flow_white_list` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `template_id` int NOT NULL DEFAULT '0' COMMENT 'flow_id',
  `domain_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '域名/ip',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1:启用 0:不启用',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1:正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `params` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '参数信息',
  `gui_flow_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '随机字符串',
  `access_restrictions` int NOT NULL DEFAULT '0' COMMENT '访问频次限制',
  `team_id` int DEFAULT NULL,
  `domain_schema` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '',
  `domain_route` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '路由',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for knowledge_data
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_data`;
CREATE TABLE `knowledge_data` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `dataset_id` int NOT NULL DEFAULT '0' COMMENT '知识数据id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '创建人',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `icon` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图标',
  `status` int NOT NULL DEFAULT '1' COMMENT '状态，1: 待训练，2: 训练中， 3: 训练成功，4: 训练失败',
  `train_status` int NOT NULL DEFAULT '0' COMMENT '最新知识问答训练状态，1: 待训练， 2: 训练中， 3: 训练成功， 4: 训练失败',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除， 1: 正常， 0: 删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `knowledge_data_dataset_id_index` (`dataset_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识数据';

-- ----------------------------
-- Table structure for knowledge_data_file
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_data_file`;
CREATE TABLE `knowledge_data_file` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `dataset_id` int NOT NULL DEFAULT '0' COMMENT '知识数据id',
  `file_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件主键id',
  `f_id` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件id',
  `process_type` int NOT NULL DEFAULT '1' COMMENT '处理类型，1: 分片 2：问答对',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '创建人',
  `file_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文档名称',
  `file_type` int NOT NULL DEFAULT '1' COMMENT '文档类型，1: 文档',
  `source` int NOT NULL DEFAULT '1' COMMENT '来源，1:本地上传，2: 在线文件',
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件地址',
  `file_size` float NOT NULL DEFAULT '0' COMMENT '文件大小',
  `split_config` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分割配置',
  `status` int NOT NULL DEFAULT '0' COMMENT '训练状态，1:待分割 2:分割中 3:分割成功 4:分割失败',
  `train_status` int NOT NULL DEFAULT '0' COMMENT '知识问答训练状态',
  `batch_no` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件上传批次id',
  `task_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '任务id',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除， 0：删除，1: 正常',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `update_interval` int NOT NULL DEFAULT '1' COMMENT '更新频率，1: 不自动更新',
  `interval_second` int NOT NULL DEFAULT '0' COMMENT '更新间隔时间戳',
  `web_url` varchar(256) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '在线网址',
  `last_updated` datetime DEFAULT NULL COMMENT '在线文件最后更新时间',
  `is_updating` int NOT NULL DEFAULT '2' COMMENT '是否正在更新，1: 是， 2: 否',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `time_to_update` int NOT NULL DEFAULT '0' COMMENT '待更新时间',
  PRIMARY KEY (`id`),
  KEY `knowledge_data_file_dataset_id_index` (`dataset_id`),
  KEY `knowledge_data_file_task_id_index` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识数据文档';

-- ----------------------------
-- Table structure for knowledge_data_qa_relation
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_data_qa_relation`;
CREATE TABLE `knowledge_data_qa_relation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `dataset_id` int NOT NULL DEFAULT '0' COMMENT '知识数据id',
  `knowledge_qa_id` int NOT NULL DEFAULT '0' COMMENT '知识问答id',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除，0: 删除， 1: 未删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `knowledge_data_qa_relation_team_id_knowledge_qa_id_index` (`team_id`,`knowledge_qa_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识数据-问答关系表';

-- ----------------------------
-- Table structure for knowledge_file_uuid_relation
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_file_uuid_relation`;
CREATE TABLE `knowledge_file_uuid_relation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `file_uuid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件uuid',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
  `file_id` int NOT NULL DEFAULT '0' COMMENT '上传实际file_id',
  `file_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件名称',
  `file_path` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件url',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `knowledge_file_uuid_relation_file_uuid_team_id_index` (`file_uuid`,`team_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- ----------------------------
-- Table structure for knowledge_qa
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_qa`;
CREATE TABLE `knowledge_qa` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
  `knowledge_qa_id` int NOT NULL DEFAULT '0' COMMENT '知识问答id',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `description` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `model_config` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '模型参数',
  `first_sentence` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开场白',
  `default_reply` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '默认回复',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态，0: 初始状态，1: 待训练 2:训练中 3:训练成功 4:训练失败 5: 文档处理中',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除，0: 删除 1: 正常',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `icon` text COLLATE utf8mb4_general_ci COMMENT '图标链接',
  `dataset_ids` text COLLATE utf8mb4_general_ci COMMENT '关联的数据库ids',
  `source` int DEFAULT '1' COMMENT '来源, 知识库1, 问答0',
  `do_sample` tinyint DEFAULT '0' COMMENT '是否采样',
  `qa_switch` tinyint DEFAULT '0' COMMENT 'QA训练开关',
  `role` varchar(512) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '角色词',
  `model_name` varchar(256) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '模型名',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',
  PRIMARY KEY (`id`),
  KEY `knowledge_qa_knowledge_qa_id_index` (`knowledge_qa_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识问答';

-- ----------------------------
-- Table structure for knowledge_quote_relation
-- ----------------------------
DROP TABLE IF EXISTS `knowledge_quote_relation`;
CREATE TABLE `knowledge_quote_relation` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `knowledge_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '知识数据id/知识问答id',
  `knowledge_type` tinyint NOT NULL DEFAULT '1' COMMENT '知识库类型，1: 知识数据， 2: 知识问答',
  `template_id` int NOT NULL DEFAULT '0' COMMENT '引用模块的id',
  `template_type` int NOT NULL DEFAULT '0' COMMENT '引用模块类型，3: flow， 6: agent',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除， 1: 未删除， 0: 删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `knowledge_quote_relation_pk2` (`template_id`,`template_type`,`knowledge_id`,`knowledge_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识库引用关系表';

-- ----------------------------
-- Table structure for login_valid_configuration
-- ----------------------------
DROP TABLE IF EXISTS `login_valid_configuration`;
CREATE TABLE `login_valid_configuration` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `url` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置url',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '方法',
  `src` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源',
  `source_type` int NOT NULL DEFAULT '1' COMMENT 'src来源类型，与src组合标识来源',
  `is_team_mapping` tinyint NOT NULL DEFAULT '0' COMMENT '是否存在团队关系映射',
  `is_project_src` tinyint NOT NULL DEFAULT '0' COMMENT '是否根据团队区分来源名称',
  `enable_bg_admin` tinyint NOT NULL DEFAULT '0' COMMENT '是否开放后台管理员权限',
  `enable_is_admin` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启自定义用户角色关系',
  `is_project_list` tinyint NOT NULL DEFAULT '0' COMMENT '是否存在多个组织',
  `request_params` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求参数json',
  `request_data` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求参数列表',
  `response_data` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '返回数据对应关系',
  `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除，0: 删除，1: 未删除',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `login_valid_configuration_src_index` (`src`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='登录对接-用户校验配置';

-- ----------------------------
-- Table structure for model
-- ----------------------------
DROP TABLE IF EXISTS `model`;
CREATE TABLE `model` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '模型表主键',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id ',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '类型 文生图: stable_diffusion 文生文:glm /GPT',
  `json_desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '模型参数',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='提示工程模型表';

-- ----------------------------
-- Table structure for prompt
-- ----------------------------
DROP TABLE IF EXISTS `prompt`;
CREATE TABLE `prompt` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '提示词表',
  `type` int NOT NULL DEFAULT '1' COMMENT '类型 1：文生文 2：文生图',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `desc` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '描述',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '内容',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1：启用 2：不启用',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `prompt_word` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '提示词：{}里的词',
  `spilt_word` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '{{}}' COMMENT '分隔符',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户表id ',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='提示工程提示词表';

-- ----------------------------
-- Table structure for prompt_image_example_dict
-- ----------------------------
DROP TABLE IF EXISTS `prompt_image_example_dict`;
CREATE TABLE `prompt_image_example_dict` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` int NOT NULL DEFAULT '0' COMMENT '上级分类的编号：0表示一级分类',
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `translation` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '英文翻译',
  `path` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '路径',
  `style` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '风格',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='图像字典表';

-- ----------------------------
-- Table structure for prompt_tags
-- ----------------------------
DROP TABLE IF EXISTS `prompt_tags`;
CREATE TABLE `prompt_tags` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '提示标签表',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签的名称',
  `tag_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '标签分类1:agent 5:api',
  `desc` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标签描述',
  `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公开标签表';

-- ----------------------------
-- Table structure for prompt_tags_relation
-- ----------------------------
DROP TABLE IF EXISTS `prompt_tags_relation`;
CREATE TABLE `prompt_tags_relation` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '标签-提示词关系表主键',
  `tag_id` int NOT NULL DEFAULT '0' COMMENT '标签id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `template_id` int NOT NULL DEFAULT '0' COMMENT '模版ID',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模版类型 1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent',
  PRIMARY KEY (`id`),
  KEY `idx_temp_id` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='公开标签和模版关系表';

-- ----------------------------
-- Table structure for prompt_user
-- ----------------------------
DROP TABLE IF EXISTS `prompt_user`;
CREATE TABLE `prompt_user` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '用户表主键',
  `uid` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户UID',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名',
  `src` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'agent' COMMENT '来源，agent：内网用户，pcw_agent：外网用户',
  `email` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邮件',
  `phone_number` varchar(14) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `avatar` varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像',
  `api_key` text COLLATE utf8mb4_general_ci COMMENT 'api-key',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，1：不删除，0：已删除',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '是否启用，0：不启用，1：启用，默认启用',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `inner_src` int NOT NULL DEFAULT '1' COMMENT '内网来源，1: idaas登陆，2: 推推',
  `salt` VARCHAR(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '开源版盐值',
  `password` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '开源版用户密码',
  `reg_reason` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT '' COMMENT '注册原因',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='平台用户表';

-- ----------------------------
-- Table structure for provider_models
-- ----------------------------
DROP TABLE IF EXISTS `provider_models`;
CREATE TABLE `provider_models` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` int unsigned NOT NULL,
  `provider_name` varchar(40) NOT NULL,
  `model_name` varchar(255) NOT NULL,
  `model_type` varchar(40) NOT NULL,
  `encrypted_config` text,
  `is_valid` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_provider_model_name` (`team_id`,`provider_name`,`model_name`,`model_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for provider_orders
-- ----------------------------
DROP TABLE IF EXISTS `provider_orders`;
CREATE TABLE `provider_orders` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint NOT NULL,
  `provider_name` varchar(40) NOT NULL,
  `account_id` bigint NOT NULL,
  `payment_product_id` varchar(191) NOT NULL,
  `payment_id` varchar(191) NOT NULL,
  `transaction_id` varchar(191) DEFAULT NULL,
  `quantity` int NOT NULL DEFAULT '1',
  `currency` varchar(40) DEFAULT NULL,
  `total_amount` int DEFAULT NULL,
  `payment_status` varchar(40) NOT NULL,
  `paid_at` datetime DEFAULT NULL,
  `pay_failed_at` datetime DEFAULT NULL,
  `refunded_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_provider_orders_team_id` (`team_id`,`provider_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for providers
-- ----------------------------
DROP TABLE IF EXISTS `providers`;
CREATE TABLE `providers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` int NOT NULL COMMENT '团队id',
  `provider_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '供应商名称',
  `encrypted_config` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '加密后的配置信息',
  `is_valid` tinyint(1) NOT NULL DEFAULT '1',
  `quota_type` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '定额',
  `quota_limit` bigint DEFAULT NULL,
  `quota_used` bigint DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `provider_type` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'custom',
  `last_used` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `team_id` (`team_id`,`provider_name`),
  UNIQUE KEY `uniq_provider_name_type_quota` (`team_id`,`provider_name`,`provider_type`,`quota_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='保存用户自定义的供应商配置信息';

-- ----------------------------
-- Table structure for qid_white_list
-- ----------------------------
DROP TABLE IF EXISTS `qid_white_list`;
CREATE TABLE `qid_white_list` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `qid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '360账号ID',
  `domain_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '域名/ip',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '1:启用 0:不启用',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1:正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for team
-- ----------------------------
DROP TABLE IF EXISTS `team`;
CREATE TABLE `team` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '团队表主键',
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '团队名',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `last_op_uid` int NOT NULL DEFAULT '0' COMMENT '最后操作人',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `team_type` tinyint NOT NULL DEFAULT '1' COMMENT '团队类型: 1:普通 2:官方 3:示例 4.个人',
  `create_type` tinyint NOT NULL COMMENT '创建方式 1: 管理员添加， 2：用户申请',
  `src` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'agent',
  `encrypt_public_key` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '团队公钥',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='团队表';

-- ----------------------------
-- Table structure for team_audit
-- ----------------------------
DROP TABLE IF EXISTS `team_audit`;
CREATE TABLE `team_audit` (
  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '团队审核表主键',
  `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '申请人ID',
  `team_name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '团队名',
  `team_type` tinyint NOT NULL DEFAULT '1' COMMENT '团队类型: 1:普通 2:官方 3:示例 4.个人(申请只能申请普通类型)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '审核状态， 1:待审核 2:通过 3:拒绝 4:撤销审核',
  `user_contact` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '申请人联系方式',
  `application_information` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '申请信息',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '1：正常 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `src` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'agent',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='团队审核表';

-- ----------------------------
-- Table structure for team_default_models
-- ----------------------------
DROP TABLE IF EXISTS `team_default_models`;
CREATE TABLE `team_default_models` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint NOT NULL,
  `provider_name` varchar(40) NOT NULL,
  `model_name` varchar(40) NOT NULL,
  `model_type` varchar(40) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for team_member_role
-- ----------------------------
DROP TABLE IF EXISTS `team_member_role`;
CREATE TABLE `team_member_role` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `team_id` int NOT NULL DEFAULT '0' COMMENT '团队id',
  `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
  `team_role_id` int NOT NULL DEFAULT '2' COMMENT '团队角色 1:项目管理员 2:项目成员 ',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除 1:未删除 0:删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='团队-成员-角色表';

-- ----------------------------
-- Table structure for team_preferred_model_providers
-- ----------------------------
DROP TABLE IF EXISTS `team_preferred_model_providers`;
CREATE TABLE `team_preferred_model_providers` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` bigint NOT NULL COMMENT '团队ID',
  `provider_name` varchar(40) NOT NULL,
  `preferred_provider_type` varchar(40) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_team_preferred_model_provider_tenant_provider` (`team_id`,`provider_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for team_template_check
-- ----------------------------
DROP TABLE IF EXISTS `team_template_check`;
CREATE TABLE `team_template_check` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `team_id` int NOT NULL COMMENT '团队ID',
  `template_type` tinyint NOT NULL DEFAULT '1' COMMENT '模版类型 1:文生文 2:文生图 3:flow 4:知识库 5:api 6:agent',
  `template_id` int NOT NULL COMMENT '模版ID ',
  `template_history_id` int unsigned NOT NULL DEFAULT '0' COMMENT '模版历史记录ID',
  `template_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模版名称',
  `template_channel` varchar(255) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发布渠道(仅展示)',
  `submit_user_id` int NOT NULL COMMENT '提交人',
  `check_user_id` int NOT NULL DEFAULT '0' COMMENT '审核ID',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `check_type` tinyint NOT NULL DEFAULT '1' COMMENT '审核类型 1:上架',
  `check_status` tinyint NOT NULL DEFAULT '1' COMMENT '审核状态 1:待审核 2:通过 3:拒绝 4:撤销审核',
  `check_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '审核理由',
  `prompt_tags_ids` tinytext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签ids eg:1,2,3',
  `src` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'agent' COMMENT '来源',
  `is_privacy` tinyint DEFAULT '1' COMMENT '是否公开配置，1-不公开，2-公开',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模版上架市场审核表';

-- ----------------------------
-- Table structure for user_white_list
-- ----------------------------
DROP TABLE IF EXISTS `user_white_list`;
CREATE TABLE `user_white_list` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `uid` varchar(256) DEFAULT NULL COMMENT 'qid',
  `list_type` tinyint DEFAULT '1' COMMENT '1: 白名单， 2：黑名单',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，1：不删除，0：已删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_white_list_pk` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for video_cloud_record
-- ----------------------------
DROP TABLE IF EXISTS `video_cloud_record`;
CREATE TABLE `video_cloud_record` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `qid` varchar(255) NOT NULL DEFAULT '0' COMMENT '用户ID',
  `main_task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '主任务id',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '阶段：1合成完成  2字幕完成 3口型开始 4口型完成 ',
  `synthesis_task_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '合成任务ID',
  `synthesis_task_objectname` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '合成任务名称',
  `synthesis_task_cover` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '合成任务封面',
  `synthesis_task_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '合成任务地址',
  `subtitles_task_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字幕任务ID',
  `subtitles_task_objectname` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字幕任务名称',
  `subtitles_task_cover` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字幕任务封面',
  `subtitles_task_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '字幕任务地址',
  `mouth_shape_task_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '对口型任务ID',
  `mouth_shape_task_objectname` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '对口型任务名称',
  `mouth_shape_task_cover` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '对口型任务封面',
  `mouth_shape_task_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '合成任务地址',
  `language` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '语言',
  `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除，1：不删除，0：已删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Table structure for action_chain_flow_api
-- ----------------------------
DROP TABLE IF EXISTS `action_chain_flow_api`;
CREATE TABLE `action_chain_flow_api` (
	`id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
	`flow_id` INT(10) UNSIGNED NULL DEFAULT '0',
	`flow_version` VARCHAR(50) NOT NULL DEFAULT '' COMMENT 'flow版本' COLLATE 'utf8mb4_0900_ai_ci',
	`block_id` INT(10) UNSIGNED NULL DEFAULT '0',
	`api_id` INT(10) UNSIGNED NULL DEFAULT '0' COMMENT '插件id',
	`api_template_id` INT(10) UNSIGNED NULL DEFAULT '0' COMMENT '动作id',
	`api_version` INT(10) UNSIGNED NULL DEFAULT '1' COMMENT '版本号',
	`created_at` DATETIME NULL DEFAULT NULL,
	`updated_at` DATETIME NULL DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `idx_template_id` (`api_template_id`) USING BTREE,
	INDEX `idx_api_id` (`api_id`) USING BTREE,
	INDEX `idx_block_id` (`block_id`) USING BTREE
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;


DROP TABLE IF EXISTS `action_task_gante`;
CREATE TABLE `action_task_gante` (
	`id` INT(10) NOT NULL AUTO_INCREMENT,
	`taskid` VARCHAR(50) NOT NULL COMMENT '任务id' COLLATE 'utf8mb4_0900_ai_ci',
	`taskname` VARCHAR(20) NOT NULL COMMENT '任务名，也就是blockid' COLLATE 'utf8mb4_0900_ai_ci',
	`startdate` BIGINT(19) NULL DEFAULT NULL COMMENT '开始时间',
	`enddate` BIGINT(19) NULL DEFAULT NULL COMMENT '结束时间',
	`status` VARCHAR(25) NOT NULL DEFAULT '"submit_success"' COMMENT '任务状态' COLLATE 'utf8mb4_0900_ai_ci',
	`created_at` DATETIME NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updated_at` DATETIME NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `idx_taskid_taskname` (`taskid`, `taskname`) USING BTREE
)COMMENT='任务运行时的时间记录，用于生成最后的甘特图' COLLATE='utf8mb4_0900_ai_ci' ENGINE=InnoDB AUTO_INCREMENT=1;


CREATE TABLE IF NOT EXISTS `agent_nami_form_relation` (
      `id` int NOT NULL AUTO_INCREMENT,
      `agent_id` int NOT NULL,
      `form_id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '表单的唯一ID',
    `created_at` datetime DEFAULT NULL,
    `updated_at` datetime DEFAULT NULL,
    `is_deleted` tinyint DEFAULT '1' COMMENT '是否删除1是可用0是删除',
    `create_user` int DEFAULT NULL COMMENT '创建人',
    PRIMARY KEY (`id`),
    KEY `agent_id` (`agent_id`,`form_id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;


CREATE TABLE IF NOT EXISTS `agent_nami_prompt_form` (
    `id` int NOT NULL AUTO_INCREMENT,
    `form_id` char(36) COLLATE utf8mb4_general_ci NOT NULL COMMENT '表单唯一id',
    `name` varchar(100) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '表单名称',
    `description` varchar(500) COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '描述',
    `prompt` text COLLATE utf8mb4_general_ci COMMENT '表单提示词',
    `form_info` longtext COLLATE utf8mb4_general_ci COMMENT '表单信息',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `create_user` int NOT NULL COMMENT '创建人',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间\\n',
    `team_id` int DEFAULT NULL COMMENT '项目id',
    `is_deleted` int NOT NULL DEFAULT '1' COMMENT '是否删除，1为正常，0为删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `agent_nami_prompt_form_pk` (`form_id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='纳米提示词表单信息';


CREATE TABLE IF NOT EXISTS `nami_ai_xie_flow_cache` (
                                                        `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                        `template_id` int NOT NULL COMMENT '模版ID ',
                                                        `qid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT 'qid',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '名称',
    `theme` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '主题',
    `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '图标',
    `log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '访问唯一id',
    `start_time` int NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time` int NOT NULL DEFAULT '0' COMMENT '结束时间',
    `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '页面配置',
    `status` tinyint NOT NULL DEFAULT '1' COMMENT '运行状态 初始化:1， 已完成:2',
    `is_deleted` tinyint NOT NULL DEFAULT '1' COMMENT '是否删除 1：不删除  0：删除',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `idx_log_id` (`log_id`) USING BTREE,
    KEY `idx_qid` (`qid`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='纳米ai写作缓存数据';


CREATE TABLE IF NOT EXISTS `nami_ai_xie_task` (
                                                  `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
                                                  `type` int NOT NULL DEFAULT '1' COMMENT '任务类型 主流任务:1， 分支任务:2',
                                                  `p_log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '主流访问唯一id',
    `c_log_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '子流访问唯一id',
    `created_at` datetime DEFAULT NULL COMMENT '创建时间',
    `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='纳米ai写作任务';

CREATE TABLE IF NOT EXISTS `agent_iframe` (
                                `id` int NOT NULL AUTO_INCREMENT,
                                `agent_id` int NOT NULL DEFAULT '0' COMMENT '智能体id',
                                `website_name` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '网站名称',
                                `website_identifier` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '网站标识符',
                                `app_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '密钥',
                                `url_address` varchar(63) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '网站域名',
                                `is_deleted` tinyint NOT NULL DEFAULT '2' COMMENT '1:删除,2:正常',
                                `created_at` datetime DEFAULT NULL,
                                `updated_at` datetime DEFAULT NULL,
                                PRIMARY KEY (`id`) USING BTREE,
                                KEY `idx_agent_id` (`agent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='网页嵌入发布表';

CREATE TABLE IF NOT EXISTS `personal_access_tokens` (
                                          `id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                                          `user_id` INT(10) NOT NULL DEFAULT '0' COMMENT '用户id',
                                          `name` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '名称' COLLATE 'utf8mb4_general_ci',
                                          `scopes` TINYTEXT NOT NULL COMMENT '权限' COLLATE 'utf8mb4_general_ci',
                                          `token_hash` VARCHAR(200) NOT NULL DEFAULT '' COMMENT 'token_hash' COLLATE 'utf8mb4_general_ci',
                                          `is_deleted` INT(10) NOT NULL DEFAULT '1' COMMENT '是否删除0是删除1正常',
                                          `expires_at` DATETIME NULL DEFAULT NULL COMMENT '失效时间',
                                          `last_used_at` DATETIME NULL DEFAULT NULL COMMENT '上次使用时间',
                                          `created_at` DATETIME NULL DEFAULT NULL COMMENT '创建时间',
                                          `updated_at` DATETIME NULL DEFAULT NULL COMMENT '更新时间',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          INDEX `idx_uniq_token` (`token_hash`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='personal access tokens' ;

SET FOREIGN_KEY_CHECKS = 1;


INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (1, 'p', 'Member', 'Ordinary', '/api/apis/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (2, 'g', 'Admin', 'Member', 'Ordinary', NULL, NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (3, 'p', 'Member', 'Official', '/api/apis/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (4, 'g', 'Admin', 'Member', 'Official', '', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (5, 'p', 'Admin', 'Personal', '/api/apis/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (12, 'p', 'SystemAdmin', 'Ordinary', '/api/apis/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (13, 'g', 'SystemSuperAdmin', 'SystemAdmin', 'Ordinary', NULL, NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (14, 'p', 'SystemAdmin', 'Official', '/api/apis/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (15, 'g', 'SystemSuperAdmin', 'SystemAdmin', 'Official', NULL, NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (16, 'p', 'SystemAdmin', 'Personal', '/api/apis/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (17, 'g', 'SystemSuperAdmin', 'SystemAdmin', 'Personal', NULL, NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (18, 'p', 'Member', 'Ordinary', '/api/flow/v2/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (19, 'p', 'Member', 'Official', '/api/flow/v2/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (20, 'p', 'Admin', 'Personal', '/api/flow/v2/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (21, 'p', 'SystemAdmin', 'Ordinary', '/api/flow/v2/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (22, 'p', 'SystemAdmin', 'Official', '/api/flow/v2/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (23, 'p', 'SystemAdmin', 'Personal', '/api/flow/v2/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (24, 'p', 'SystemAdmin', 'Ordinary', '/api/flow/v2/block_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (25, 'p', 'SystemAdmin', 'Official', '/api/flow/v2/block_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (26, 'p', 'SystemAdmin', 'Personal', '/api/flow/v2/block_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (27, 'p', 'SystemAdmin', 'Ordinary', '/api/apis/list', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (28, 'p', 'SystemAdmin', 'Official', '/api/apis/list', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (29, 'p', 'SystemAdmin', 'Personal', '/api/apis/list', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (30, 'p', 'SystemAdmin', 'Ordinary', '/api/flow/v2/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (31, 'p', 'SystemAdmin', 'Official', '/api/flow/v2/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (32, 'p', 'SystemAdmin', 'Personal', '/api/flow/v2/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (33, 'p', 'Member', 'Ordinary', '/api/prompt/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (34, 'p', 'Member', 'Official', '/api/prompt/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (35, 'p', 'Admin', 'Personal', '/api/prompt/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (36, 'p', 'SystemAdmin', 'Ordinary', '/api/prompt/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (37, 'p', 'SystemAdmin', 'Ordinary', '/api/prompt/text/list_all', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (38, 'p', 'SystemAdmin', 'Ordinary', '/api/prompt/text2img/list/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (39, 'p', 'SystemAdmin', 'Ordinary', '/api/prompt/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (40, 'p', 'SystemAdmin', 'Official', '/api/prompt/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (41, 'p', 'SystemAdmin', 'Official', '/api/prompt/text/list_all', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (42, 'p', 'SystemAdmin', 'Official', '/api/prompt/text2img/list/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (43, 'p', 'SystemAdmin', 'Official', '/api/prompt/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (44, 'p', 'SystemAdmin', 'Personal', '/api/prompt/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (45, 'p', 'SystemAdmin', 'Personal', '/api/prompt/text/list_all', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (46, 'p', 'SystemAdmin', 'Personal', '/api/prompt/text2img/list/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (47, 'p', 'SystemAdmin', 'Personal', '/api/prompt/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (48, 'p', 'Member', 'Ordinary', '/api/front/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (49, 'p', 'Member', 'Official', '/api/front/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (50, 'p', 'Admin', 'Personal', '/api/front/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (51, 'p', 'Member', 'Ordinary', '/api/service_monitoring/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (52, 'p', 'Member', 'Official', '/api/service_monitoring/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (53, 'p', 'Admin', 'Personal', '/api/service_monitoring/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (54, 'p', 'SystemAdmin', 'Ordinary', '/api/service_monitoring/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (55, 'p', 'SystemAdmin', 'Official', '/api/service_monitoring/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (56, 'p', 'SystemAdmin', 'Personal', '/api/service_monitoring/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (57, 'p', 'Member', 'Ordinary', '/api/deployment/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (58, 'p', 'Member', 'Official', '/api/deployment/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (59, 'p', 'Admin', 'Personal', '/api/deployment/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (66, 'p', 'SystemAdmin', 'Ordinary', '/api/deployment/detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (67, 'p', 'SystemAdmin', 'Official', '/api/deployment/detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (68, 'p', 'SystemAdmin', 'Personal', '/api/deployment/detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (75, 'p', 'SystemAdmin', 'Ordinary', '/api/deployment/list', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (76, 'p', 'SystemAdmin', 'Official', '/api/deployment/list', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (77, 'p', 'SystemAdmin', 'Personal', '/api/deployment/list', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (81, 'p', 'SystemAdmin', 'Ordinary', '/api/deployment/chain_deployed_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (82, 'p', 'SystemAdmin', 'Official', '/api/deployment/chain_deployed_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (83, 'p', 'SystemAdmin', 'Personal', '/api/deployment/chain_deployed_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (85, 'p', 'Member', 'Ordinary', '/api/knowledge_data/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (86, 'p', 'Member', 'Official', '/api/knowledge_data/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (87, 'p', 'Admin', 'Personal', '/api/knowledge_data/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (88, 'p', 'Member', 'Ordinary', '/v1/extract', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (89, 'p', 'Member', 'Official', '/v1/extract', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (90, 'p', 'Admin', 'Personal', '/v1/extract', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (91, 'p', 'Member', 'Ordinary', '/v1/extract_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (92, 'p', 'Member', 'Official', '/v1/extract_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (93, 'p', 'Admin', 'Personal', '/v1/extract_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (94, 'p', 'Member', 'Ordinary', '/v1/doc/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (95, 'p', 'Member', 'Official', '/v1/doc/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (96, 'p', 'Admin', 'Personal', '/v1/doc/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (97, 'p', 'Member', 'Ordinary', '/v1/qa_match_test', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (98, 'p', 'Member', 'Official', '/v1/qa_match_test', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (99, 'p', 'Admin', 'Personal', '/v1/qa_match_test', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (100, 'p', 'Member', 'Ordinary', '/api/knowledge_qa/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (101, 'p', 'Member', 'Official', '/api/knowledge_qa/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (102, 'p', 'Admin', 'Personal', '/api/knowledge_qa/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (103, 'p', 'Member', 'Ordinary', '/v1/train', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (104, 'p', 'Member', 'Official', '/v1/train', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (105, 'p', 'Admin', 'Personal', '/v1/train', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (106, 'p', 'Member', 'Ordinary', '/v1/train_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (107, 'p', 'Member', 'Official', '/v1/train_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (108, 'p', 'Admin', 'Personal', '/v1/train_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (109, 'p', 'Member', 'Ordinary', '/v1/chat_stream', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (110, 'p', 'Member', 'Official', '/v1/chat_stream', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (111, 'p', 'Admin', 'Personal', '/v1/chat_stream', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (112, 'p', 'Member', 'Ordinary', '/api/chat', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (113, 'p', 'Member', 'Official', '/api/chat', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (114, 'p', 'Admin', 'Personal', '/api/chat', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (115, 'p', 'Member', 'Ordinary', '/v1/knowledge_fragment*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (116, 'p', 'Member', 'Official', '/v1/knowledge_fragment*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (117, 'p', 'Admin', 'Personal', '/v1/knowledge_fragment*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (118, 'p', 'Member', 'Ordinary', '/v1/qa*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (119, 'p', 'Member', 'Official', '/v1/qa*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (120, 'p', 'Admin', 'Personal', '/v1/qa*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (121, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge_data/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (122, 'p', 'SystemAdmin', 'Official', '/api/knowledge_data/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (123, 'p', 'SystemAdmin', 'Personal', '/api/knowledge_data/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (124, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge_data/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (125, 'p', 'SystemAdmin', 'Official', '/api/knowledge_data/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (126, 'p', 'SystemAdmin', 'Personal', '/api/knowledge_data/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (127, 'p', 'SystemAdmin', 'Ordinary', '/v1/extract_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (128, 'p', 'SystemAdmin', 'Official', '/v1/extract_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (129, 'p', 'SystemAdmin', 'Personal', '/v1/extract_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (130, 'p', 'SystemAdmin', 'Ordinary', '/v1/doc/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (131, 'p', 'SystemAdmin', 'Official', '/v1/doc/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (132, 'p', 'SystemAdmin', 'Personal', '/v1/doc/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (133, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge_data/file_info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (134, 'p', 'SystemAdmin', 'Official', '/api/knowledge_data/file_info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (135, 'p', 'SystemAdmin', 'Personal', '/api/knowledge_data/file_info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (136, 'p', 'SystemAdmin', 'Ordinary', '/v1/doc/list_by_age', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (137, 'p', 'SystemAdmin', 'Official', '/v1/doc/list_by_age', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (138, 'p', 'SystemAdmin', 'Personal', '/v1/doc/list_by_age', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (139, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge_qa/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (140, 'p', 'SystemAdmin', 'Official', '/api/knowledge_qa/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (141, 'p', 'SystemAdmin', 'Personal', '/api/knowledge_qa/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (142, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge_qa/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (143, 'p', 'SystemAdmin', 'Official', '/api/knowledge_qa/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (144, 'p', 'SystemAdmin', 'Personal', '/api/knowledge_qa/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (145, 'p', 'SystemAdmin', 'Ordinary', '/v1/knowledge_fragment', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (146, 'p', 'SystemAdmin', 'Official', '/v1/knowledge_fragment', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (147, 'p', 'SystemAdmin', 'Personal', '/v1/knowledge_fragment', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (148, 'p', 'SystemAdmin', 'Ordinary', '/v1/knowledge_fragment/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (149, 'p', 'SystemAdmin', 'Official', '/v1/knowledge_fragment/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (150, 'p', 'SystemAdmin', 'Personal', '/v1/knowledge_fragment/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (151, 'p', 'SystemAdmin', 'Ordinary', '/v1/qa', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (152, 'p', 'SystemAdmin', 'Official', '/v1/qa', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (153, 'p', 'SystemAdmin', 'Personal', '/v1/qa', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (154, 'p', 'SystemAdmin', 'Ordinary', '/v1/qa/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (155, 'p', 'SystemAdmin', 'Official', '/v1/qa/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (156, 'p', 'SystemAdmin', 'Personal', '/v1/qa/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (160, 'p', 'Member', 'Ordinary', '/api/user/check_user_permission', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (161, 'p', 'Member', 'Official', '/api/user/check_user_permission', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (162, 'p', 'Admin', 'Personal', '/api/user/check_user_permission', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (163, 'p', 'SystemAdmin', 'Ordinary', '/api/user/check_user_permission', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (164, 'p', 'SystemAdmin', 'Official', '/api/user/check_user_permission', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (165, 'p', 'SystemAdmin', 'Personal', '/api/user/check_user_permission', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (166, 'p', 'SystemAdmin', 'Ordinary', '/api/apis/publish_detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (167, 'p', 'SystemAdmin', 'Official', '/api/apis/publish_detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (168, 'p', 'SystemAdmin', 'Personal', '/api/apis/publish_detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (169, 'p', 'SystemAdmin', 'Ordinary', '/api/prompt/text2img/list_all', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (170, 'p', 'SystemAdmin', 'Official', '/api/prompt/text2img/list_all', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (171, 'p', 'SystemAdmin', 'Personal', '/api/prompt/text2img/list_all', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (172, 'p', 'Member', 'Ordinary', '/api/agent_flow/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (173, 'p', 'Member', 'Official', '/api/agent_flow/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (174, 'p', 'Admin', 'Personal', '/api/agent_flow/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (175, 'p', 'SystemAdmin', 'Ordinary', '/api/agent_flow/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (176, 'p', 'SystemAdmin', 'Official', '/api/agent_flow/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (177, 'p', 'SystemAdmin', 'Personal', '/api/agent_flow/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (178, 'p', 'Member', 'Ordinary', '/api/agent_flow/copy', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (179, 'p', 'Member', 'Official', '/api/agent_flow/copy', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (180, 'p', 'Admin', 'Personal', '/api/agent_flow/copy', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (181, 'p', 'Admin', 'Ordinary', '/api/member/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (182, 'p', 'Admin', 'Official', '/api/member/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (191, 'p', 'SystemSuperAdmin', 'System', '/api/admin/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (192, 'p', 'SystemAdmin', 'System', '/api/team*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (193, 'g', 'SystemSuperAdmin', 'SystemAdmin', 'System', NULL, NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (194, 'p', 'SystemAdmin', 'Ordinary', '/api/flow/v2/publish_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (195, 'p', 'SystemAdmin', 'Official', '/api/flow/v2/publish_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (196, 'p', 'SystemAdmin', 'Personal', '/api/flow/v2/publish_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (197, 'p', 'SystemAdmin', 'Ordinary', '/api/flow/v2/status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (198, 'p', 'SystemAdmin', 'Official', '/api/flow/v2/status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (199, 'p', 'SystemAdmin', 'Personal', '/api/flow/v2/status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (200, 'p', 'SystemAdmin', 'System', '/api/market-audits*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (201, 'p', 'SystemAdmin', 'System', '/api/feedback-list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (202, 'p', 'SystemAdmin', 'System', '/api/team-audits*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (203, 'p', 'Member', 'Ordinary', '/api/v2/agents/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (204, 'p', 'Member', 'Official', '/api/v2/agents/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (205, 'p', 'Admin', 'Personal', '/api/v2/agents/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (206, 'p', 'Member', 'Ordinary', '/api/v2/agents', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (207, 'p', 'Member', 'Official', '/api/v2/agents', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (208, 'p', 'Admin', 'Personal', '/api/v2/agents', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (209, 'p', 'SystemAdmin', 'Ordinary', '/api/v2/agents', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (210, 'p', 'SystemAdmin', 'Official', '/api/v2/agents', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (211, 'p', 'SystemAdmin', 'Personal', '/api/v2/agents', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (212, 'p', 'SystemAdmin', 'Ordinary', '/api/v2/agents/detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (213, 'p', 'SystemAdmin', 'Official', '/api/v2/agents/detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (214, 'p', 'SystemAdmin', 'Personal', '/api/v2/agents/detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (215, 'p', 'SystemAdmin', 'Ordinary', '/api/v2/agents/publish_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (216, 'p', 'SystemAdmin', 'Official', '/api/v2/agents/publish_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (217, 'p', 'SystemAdmin', 'Personal', '/api/v2/agents/publish_detail', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (218, 'p', 'SystemAdmin', 'System', '/api/member/system/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (221, 'p', 'SystemAdmin', 'Ordinary', '/api/v2/agents/get_agent_trigger_words', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (222, 'p', 'SystemAdmin', 'Official', '/api/v2/agents/get_agent_trigger_words', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (223, 'p', 'SystemAdmin', 'Personal', '/api/v2/agents/get_agent_trigger_words', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (224, 'p', 'SystemAdmin', 'System', '/api/member/system/add_member', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (225, 'p', 'SystemAdmin', 'System', '/api/member/system/delete', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (226, 'p', 'SystemAdmin', 'System', '/api/member/system/update', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (236, 'p', 'Member', 'Ordinary', '/v1/generate_similar_question', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (237, 'p', 'Member', 'Official', '/v1/generate_similar_question', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (238, 'p', 'Admin', 'Personal', '/v1/generate_similar_question', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (239, 'p', 'Member', 'Ordinary', '/v1/similar_question/*', 'PUT', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (240, 'p', 'Member', 'Official', '/v1/similar_question/*', 'PUT', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (241, 'p', 'Admin', 'Personal', '/v1/similar_question/*', 'PUT', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (242, 'p', 'Member', 'Ordinary', '/v1/similar_question', 'DELETE', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (243, 'p', 'Member', 'Official', '/v1/similar_question', 'DELETE', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (244, 'p', 'Admin', 'Personal', '/v1/similar_question', 'DELETE', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (245, 'p', 'Member', 'Ordinary', '/v1/similar_question', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (246, 'p', 'Member', 'Official', '/v1/similar_question', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (247, 'p', 'Admin', 'Personal', '/v1/similar_question', 'POST', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (248, 'p', 'Member', 'Ordinary', '/api/knowledge/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (249, 'p', 'Member', 'Official', '/api/knowledge/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (250, 'p', 'Admin', 'Personal', '/api/knowledge/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (251, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (252, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (253, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/segment_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (254, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/qa_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (255, 'p', 'SystemAdmin', 'Official', '/api/knowledge/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (256, 'p', 'SystemAdmin', 'Official', '/api/knowledge/status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (257, 'p', 'SystemAdmin', 'Official', '/api/knowledge/segment_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (258, 'p', 'SystemAdmin', 'Official', '/api/knowledge/qa_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (259, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (260, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (261, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/segment_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (262, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/qa_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (263, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/obtain_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (264, 'p', 'SystemAdmin', 'Official', '/api/knowledge/obtain_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (265, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/obtain_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (266, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/knowledge_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (267, 'p', 'SystemAdmin', 'Official', '/api/knowledge/knowledge_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (268, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/knowledge_list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (269, 'p', 'SystemAdmin', 'Ordinary', '/api/knowledge/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (270, 'p', 'SystemAdmin', 'Official', '/api/knowledge/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (271, 'p', 'SystemAdmin', 'Personal', '/api/knowledge/info/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (272, 'p', 'SystemAdmin', 'Ordinary', '/v1/train_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (273, 'p', 'SystemAdmin', 'Official', '/v1/train_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (274, 'p', 'SystemAdmin', 'Personal', '/v1/train_task_status/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (275, 'p', 'Member', 'Ordinary', '/api/auth/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (276, 'p', 'Member', 'Official', '/api/auth/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (277, 'p', 'Admin', 'Personal', '/api/auth/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (278, 'p', 'SystemAdmin', 'System', '/api/channel*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (279, 'p', 'SystemAdmin', 'System', '/api/admin_agents/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (280, 'p', 'Member', 'Ordinary', '/api/v2/channel-tag/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (281, 'p', 'Member', 'Official', '/api/v2/channel-tag/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (282, 'p', 'Admin', 'Personal', '/api/v2/channel-tag/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (283, 'p', 'Member', 'Ordinary', '/api/flow/v3/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (284, 'p', 'Member', 'Official', '/api/flow/v3/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (285, 'p', 'Admin', 'Personal', '/api/flow/v3/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (286, 'p', 'Member', 'Ordinary', '/api/file/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (287, 'p', 'Member', 'Official', '/api/file/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (288, 'p', 'Admin', 'Personal', '/api/file/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (289, 'p', 'Member', 'Ordinary', '/api/card/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (290, 'p', 'Member', 'Official', '/api/card/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (291, 'p', 'Admin', 'Personal', '/api/card/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (292, 'p', 'SystemAdmin', 'Ordinary', '/api/card/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (293, 'p', 'SystemAdmin', 'Official', '/api/card/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (294, 'p', 'SystemAdmin', 'Personal', '/api/card/detail/*', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (295, 'p', 'SystemAdmin', 'Ordinary', '/api/card/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (296, 'p', 'SystemAdmin', 'Official', '/api/card/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (297, 'p', 'SystemAdmin', 'Personal', '/api/card/list', 'GET', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (298, 'p', 'SystemSuperAdmin', 'System', '/api/external_user/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (299, 'p', 'Member', 'Ordinary', '/api/inner_flow/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (300, 'p', 'Member', 'Official', '/api/inner_flow/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (301, 'p', 'Admin', 'Personal', '/api/inner_flow/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (302, 'p', 'Member', 'Ordinary', '/api/application_flow/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (303, 'p', 'Member', 'Official', '/api/application_flow/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (304, 'p', 'Admin', 'Personal', '/api/application_flow/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (305, 'p', 'Member', 'Ordinary', '/api/v2/agent/report/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (306, 'p', 'Member', 'Official', '/api/v2/agent/report/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (307, 'p', 'Admin', 'Personal', '/api/v2/agent/report/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Ordinary', '/api/apis/v2/version_info', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Official', '/api/apis/v2/version_info', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Personal', '/api/apis/v2/version_info', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Ordinary', '/api/apis/v2/version_template_info_public', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Official', '/api/apis/v2/version_template_info_public', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Personal', '/api/apis/v2/version_template_info_public', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Member', 'Ordinary', '/api/apis/v2/version_template_info_public', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Member', 'Official', '/api/apis/v2/version_template_info_public', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Admin', 'Personal', '/api/apis/v2/version_template_info_public', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Personal', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Official', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Ordinary', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Admin', 'Personal', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Member', 'Official', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Member', 'Ordinary', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Admin', 'Ordinary', '/api/prompt-tags/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Personal', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Official', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'SystemAdmin', 'Ordinary', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Admin', 'Personal', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Member', 'Official', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Member', 'Ordinary', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);
INSERT INTO `casbin_rule` (`id`, `ptype`, `v0`, `v1`, `v2`, `v3`, `v4`, `v5`) VALUES (null, 'p', 'Admin', 'Ordinary', '/api/v2/personal_access_tokens/*', '*', NULL, NULL);


-- 正在导出表  agent2_prd.prompt_image_example_dict 的数据：~666 rows (大约)
INSERT INTO `prompt_image_example_dict` (`id`, `parent_id`, `name`, `translation`, `path`, `style`, `created_at`, `updated_at`) VALUES
	(1, 0, '容貌', NULL, '1', NULL, '2023-04-18 11:37:23', '2023-04-18 11:37:25'),
	(2, 1, '头发', NULL, '2,1', NULL, '2023-04-18 11:38:14', '2023-04-18 11:38:15'),
	(3, 1, '头饰', NULL, '3,1', NULL, '2023-04-18 11:41:06', '2023-04-18 11:41:08'),
	(4, 1, '眼睛', NULL, '4,1', NULL, '2023-04-18 11:41:30', '2023-04-18 11:41:31'),
	(5, 1, '耳朵', NULL, '5,1', NULL, '2023-04-18 11:41:48', '2023-04-18 11:41:50'),
	(6, 1, '表情', NULL, '6,1', NULL, '2023-04-18 11:42:17', '2023-04-18 11:42:18'),
	(7, 2, '条染', 'streaked hair', '7,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(8, 2, '多彩头发', 'multicolored hair', '8,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(9, 2, '内侧染色', 'colored inner hair', '9,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(10, 2, '金发', 'blonde hair', '10,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(11, 2, '银发', 'silver hair', '11,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(12, 2, '灰发', 'grey hair', '12,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(13, 2, '白发', 'white hair', '13,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(14, 2, '茶发', 'brown hair', '14,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(15, 2, '浅褐发', 'light brown hair', '15,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(16, 2, '黑发', 'black hair', '16,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(17, 2, '紫发', 'purple hair', '17,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(18, 2, '红发', 'red hair', '18,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(19, 2, '蓝发/水色发', 'blue hair', '19,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(20, 2, '深蓝发', 'dark blue hair', '20,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(21, 2, '浅蓝发', 'light blue hair', '21,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(22, 2, '绿发', 'green hair', '22,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(23, 2, '粉发', 'pink hair', '23,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(24, 2, '渐变发色', 'gradient hair', '24,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(25, 2, '彩虹发', 'rainbow hair', '25,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(26, 2, '呆毛', 'ahoge', '26,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(27, 2, '非对称发型', 'asymmetrical hair', '27,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(28, 2, '刘海', 'bangs', '28,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(29, 2, '齐刘海', 'blunt bangs', '29,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(30, 2, '辫子', 'braid', '30,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(31, 2, '编织马尾辫', 'braided ponytail', '31,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(32, 2, '卷发', 'curly hair', '32,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(33, 2, '窗帘/瀑布发型', 'curtained hair', '33,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(34, 2, '双团子头', 'double bun', '34,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(35, 2, '钻头卷/公主卷', 'drill hair', '35,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(36, 2, '双钻头卷', 'twin drills', '36,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(37, 2, '多钻头卷', 'quad drills', '37,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(38, 2, '单侧钻头卷', 'side drill', '38,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(39, 2, '法式辫', 'french braid', '39,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(40, 2, '耳后发', 'hair behind ear', '40,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(41, 2, '眼间刘海', 'hair between eyes', '41,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(42, 2, '交错刘海', 'crossed bangs', '42,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(43, 2, '团子头', 'hair bun', '43,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(44, 2, '进气口发型', 'hair intakes', '44,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(45, 2, '披肩发', 'hair over shoulder', '45,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(46, 2, '姬发式', 'hime cut', '46,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(47, 2, '长发', 'long hair', '47,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(48, 2, '凌乱发型', 'messy hair', '48,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(49, 2, '分开的刘海', 'parted bangs', '49,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(50, 2, '马尾', 'ponytail', '50,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(51, 2, '短发', 'short hair', '51,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(52, 2, '短马尾', 'short ponytail', '52,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(53, 2, '朝一个方向的刘海', 'side swept bangs', '53,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(54, 2, '侧马尾', 'side ponytail', '54,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(55, 2, '双辫子', 'twin braids', '55,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(56, 2, '双马尾', 'twintails', '56,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(57, 2, '很长的头发', 'very long hair', '57,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(58, 2, '前马尾', 'front ponytail', '58,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(59, 2, '短双马尾', 'short twintails', '59,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(60, 2, '折叠马尾', 'folded ponytail', '60,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(61, 2, '四马尾', 'quad tails', '61,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(62, 2, '单辫', 'single braid', '62,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(63, 2, '低双辫', 'low twin braids', '63,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(64, 2, '侧辫', 'side braid', '64,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(65, 2, '冠型织辫', 'crown braid', '65,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(66, 2, '脏辫', 'dreadlocks', '66,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(67, 2, '锥形发髻', 'cone hair bun', '67,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(68, 2, '辫子髻', 'braided bun', '68,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(69, 2, '圆环发髻', 'doughnut hair bun', '69,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(70, 2, '心形发髻', 'heart hair bun', '70,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(71, 2, '自然卷', 'wavy hair', '71,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(72, 2, '不对称刘海', 'asymmetrical bangs', '72,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(73, 2, '扫浏海', 'swept bangs', '73,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(74, 2, '耳前发', 'sidelocks', '74,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(75, 2, '单耳前发', 'single sidelock', '75,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(76, 2, '头发后梳', 'hair pulled back', '76,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(77, 2, '侧发后梳', 'half updo', '77,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(78, 2, '一侧绑发', 'hair one side up', '78,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(79, 2, '双侧绑发', 'hair two side up', '79,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(80, 2, '散发', 'hair spread out', '80,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(81, 2, '漂浮的头发', 'floating hair', '81,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(82, 2, '直发', 'straight hair', '82,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(83, 2, '头发很多的', 'big hair', '83,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(84, 2, '水晶状的头发', 'crystal hair', '84,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(85, 2, '富有表现力的头发', 'expressive hair', '85,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(86, 2, '头发遮着双眼', 'hair over eyes', '86,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(87, 2, '强调一缕一缕感的发型/发丝', 'hair strand', '87,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(88, 2, '头发遮住了一只眼睛', 'hair over one eye', '88,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(89, 2, '有光泽的头发', 'shiny hair', '89,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(90, 2, '湿头发', 'wet hair', '90,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(91, 2, '垂下的长鬈发', 'hair slicked back', '91,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(92, 2, '披在两侧的两条辫子', 'high ponytail', '92,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(93, 2, '侧马尾', 'long braid', '93,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(94, 2, '直发', 'low-tied long hair', '94,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(95, 2, '低扎马尾', 'low ponytail', '95,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(96, 2, '低扎双尾', 'low twintails', '96,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(97, 2, '中等长发', 'medium hair', '97,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(98, 2, '垂下的长鬈发', 'ringlets', '98,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(99, 2, '披在两侧的两条辫子', 'side braids', '99,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(100, 2, '披在两侧的发髻', 'side bun', '100,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(101, 2, '尾部散开的单马尾发型', 'split ponytail', '101,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(102, 2, '小型双股辫', 'two side up', '102,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(103, 2, '超长的头发', 'absurdly long hair', '103,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(104, 2, '云絮状发型', 'cloud hair', '104,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(105, 2, '外卷发型', 'flipped hair', '105,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(106, 2, '触手头发', 'tentacle hair', '106,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(107, 2, '很短的头发', 'very short hair', '107,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(108, 2, '掀起的刘海', 'bangs pinned back', '108,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(109, 2, '辫子刘海', 'braided bangs', '109,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(110, 2, '斜刘海', 'diagonal bangs', '110,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(111, 2, '单侧进气口发型', 'single hair intake', '111,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(112, 2, '耳状头发', 'hair ears', '112,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(113, 2, '秃头', 'bald', '113,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(114, 2, '秃头女孩', 'bald girl', '114,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(115, 2, '锅盖头', 'bowl cut', '115,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(116, 2, '寸头', 'buzz cut', '116,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(117, 2, '丁髷', 'chonmage', '117,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(118, 2, '平头/板寸头', 'crew cut', '118,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(119, 2, '平顶', 'flattop', '119,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(120, 2, '河童头', 'okappa', '120,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(121, 2, '精灵头', 'pixie cut', '121,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(122, 2, '帽盔式发型', 'undercut', '122,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(123, 2, '波波头', 'bob cut', '123,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(124, 2, '玉米垄发型', 'cornrows', '124,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(125, 2, '鲻鱼头', 'mullet', '125,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(126, 2, '弓形头发', 'bow-shaped hair', '126,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(127, 2, '前辫', 'front braid', '127,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(128, 2, '多股(麻花)辫', 'multiple braids', '128,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(129, 2, '三股辫', 'tri braids', '129,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(130, 2, '四股辫', 'quad braids', '130,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(131, 2, '三发髻', 'triple bun', '131,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(132, 2, '发圈', 'hair rings', '132,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(133, 2, '扎头发', 'tied hair', '133,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(134, 2, '单发圈', 'single hair ring', '134,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(135, 2, '只扎了一边的头发', 'one side up', '135,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(136, 2, '低辫长发', 'low-braided long hair', '136,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(137, 2, '角发', 'mizura', '137,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(138, 2, '多扎头发', 'multi-tied hair', '138,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(139, 2, '日本发', 'nihongami', '139,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(140, 2, '丸子头', 'topknot', '140,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(141, 2, '两股辫子大小不一', 'uneven twintails', '141,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(142, 2, '有三股辫子', 'tri tails', '142,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(143, 2, '有五股辫子', 'quin tails', '143,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(144, 2, '鸟窝头/爆炸头', 'afro', '144,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(145, 2, '超大鸟窝头', 'huge afro', '145,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(146, 2, '蜂窝头', 'beehive hairdo', '146,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(147, 2, '蓬帕杜发型', 'pompadour', '147,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(148, 2, '蓬松感油头', 'quiff', '148,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(149, 2, '在摆动的头发', 'hair flaps', '149,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(150, 2, '带着尖角的发型', 'pointy hair', '150,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(151, 2, '刺刺的头发', 'spiked hair', '151,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(152, 2, '美人尖', 'widow\'s peak', '152,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(153, 2, '心形呆毛', 'heart ahoge', '153,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(154, 2, '大呆毛', 'huge ahoge', '154,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(155, 2, '多根呆毛', 'antenna hair', '155,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(156, 2, '遮盖头发稀少部分', 'comb over', '156,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(157, 2, '莫霍克发型', 'mohawk', '157,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(158, 2, '孤颈毛', 'lone nape hair', '158,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(159, 2, '头发比基尼', 'hair bikini', '159,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(160, 2, '头发围巾', 'hair scarf', '160,2,1', '普通', '2023-04-18 13:43:23', '2023-04-18 13:43:23'),
	(161, 3, '头顶光环', 'halo', '161,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(162, 3, '东金帽子', 'tokin hat', '162,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(163, 3, '迷你礼帽', 'mini top hat', '163,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(164, 3, '贝雷帽', 'beret', '164,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(165, 3, '兜帽', 'hood', '165,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(166, 3, '护士帽', 'nurse cap', '166,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(167, 3, '三重冕', 'tiara', '167,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(168, 3, '皇冠', 'crown', '168,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(169, 3, '发卡', 'hairpin', '169,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(170, 3, '头箍', 'hairband', '170,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(171, 3, '发夹', 'hairclip', '171,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(172, 3, '发带', 'hair ribbon', '172,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(173, 3, '发花', 'hair flower', '173,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(174, 3, '头饰', 'hair ornament', '174,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(175, 3, '蝴蝶结发饰', 'hair bow', '175,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(176, 3, '女仆头饰', 'maid headdress', '176,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(177, 3, '丝带', 'ribbon', '177,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(178, 3, '太阳镜', 'sunglasses', '178,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(179, 3, '眼罩', 'blindfold', '179,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(180, 3, '单眼罩', 'eyepatch', '180,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(181, 3, '面具/眼罩/口罩', 'mask', '181,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(182, 3, '首饰', 'jewelry', '182,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(183, 3, '铃铛', 'bell', '183,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(184, 3, '面纹', 'facepaint', '184,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(185, 3, '兽角', 'horns', '185,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(186, 3, '鹿角', 'antlers', '186,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(187, 3, '三叶草发饰', 'clover hair ornament', '187,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(188, 3, '月牙发饰', 'crescent hair ornament', '188,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(189, 3, '恶魔的角', 'demon horns', '189,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(190, 3, '蓬莱玉枝', 'jeweled branch of hourai', '190,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(191, 3, '鱼形发饰', 'fish hair ornament', '191,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(192, 3, '额前有宝石', 'forehead jewel', '192,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(193, 3, '额前有图案', 'forehead mark', '193,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(194, 3, '护额', 'forehead protector', '194,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(195, 3, '簪子', 'kanzashi', '195,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(196, 3, '头绳', 'hair bobbles', '196,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(197, 3, '头发上成对的像无线蓝牙的发饰', 'hairpods', '197,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(198, 3, '头发上系着铃铛', 'hair bell', '198,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(199, 3, '心形眼镜', 'heart-shaped eyewear', '199,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(200, 3, '护目镜', 'goggles', '200,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(201, 3, '无框眼镜', 'rimless eyewear', '201,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(202, 3, '下半无框眼镜', 'over-rim eyewear', '202,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(203, 3, '卡米纳墨镜', 'kamina shades', '203,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(204, 3, '头上别着护目镜', 'goggles on head', '204,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(205, 3, '帽子上别着护目镜', 'goggles on headwear', '205,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(206, 3, '戴着头戴显示设备', 'head mounted display', '206,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(207, 3, '贴有绷带的脸', 'bandage on', '207,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(208, 3, '缠着绷带的单眼', 'bandage over one eye', '208,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(209, 3, '眼睛上的疤痕', 'scar across eye', '209,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(210, 3, '脸颊上的疤痕', 'scar on cheek', '210,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(211, 3, '蒙住的眼', 'covered eyes', '211,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(212, 3, '医用口罩', 'surgical mask', '212,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(213, 3, '口罩', 'mouth mask', '213,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(214, 3, '面纱', 'mouth veil', '214,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(215, 3, '厚如玻璃瓶底的圆眼镜', 'coke-bottle glasses', '215,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(216, 3, '天狗面具', 'tengu mask', '216,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(217, 3, '狐狸面具', 'fox mask', '217,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(218, 3, '掀到头上的面具', 'mask on head', '218,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(219, 3, '拉着口罩', 'mask pull', '219,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(220, 3, '摘下的面具', 'mask removed', '220,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(221, 3, '防毒面具', 'gas mask', '221,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(222, 3, '锚形项圈', 'anchor choker', '222,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(223, 3, '珠子项链', 'bead necklace', '223,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(224, 3, '耳机', 'headphones', '224,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(225, 3, '从后脑戴上的耳机', 'behind-the-head headphones', '225,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(226, 3, '脖子上挂着口哨', 'whistle around neck', '226,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(227, 3, '兽耳头罩', 'animal hood', '227,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(228, 3, '戴眼镜的', 'bespectacled', '228,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(229, 3, '软呢帽', 'fedora', '229,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(230, 3, '女巫帽', 'witch hat', '230,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(231, 3, '法师帽', 'wizard hat', '231,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(232, 3, '带翅膀的头盔', 'winged helmet', '232,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(233, 3, '放下的兜帽', 'hood down', '233,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(234, 3, '戴起来的兜帽', 'hood up', '234,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(235, 3, '水手帽', 'sailor hat', '235,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(236, 3, '圣诞帽', 'santa hat', '236,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(237, 3, '类似警帽的帽子', 'peaked cap', '237,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(238, 3, '护肘', 'elbow pads', '238,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(239, 3, '龙角', 'dragon horns', '239,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(240, 3, '眼镜别在头上', 'eyewear on head', '240,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(241, 3, '眼角有痣', 'mole under eye', '241,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(242, 3, '嘴角有痣/美人痣', 'mole under mouth', '242,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(243, 3, 'x发饰', 'x hair ornament', '243,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(244, 3, '黑色发带', 'black hairband', '244,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(245, 3, '发箍', 'hair scrunchie', '245,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(246, 3, '白色发带', 'white hairband', '246,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(247, 3, '发带', 'hair tie', '247,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(248, 3, '青蛙发饰', 'frog hair ornament', '248,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(249, 3, '食物发饰', 'food-themed hair ornament', '249,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(250, 3, '星星发饰', 'star hair ornament', '250,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(251, 3, '心形发饰', 'heart hair ornament', '251,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(252, 3, '红色发带', 'red hairband', '252,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(253, 3, '蝴蝶发饰', 'butterfly hair ornament', '253,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(254, 3, '蛇发饰', 'snake hair ornament', '254,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(255, 3, '洛丽塔发带', 'lolita hairband', '255,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(256, 3, '羽毛头饰', 'feather hair ornament', '256,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(257, 3, '蓝色发带', 'blue hairband', '257,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(258, 3, '锚发饰', 'anchor hair ornament', '258,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(259, 3, '叶发饰', 'leaf hair ornament', '259,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(260, 3, '兔子头饰', 'bunny hair ornament', '260,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(261, 3, '骷髅头饰', 'skull hair ornament', '261,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(262, 3, '黄色发带', 'yellow hairband', '262,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(263, 3, '粉色发带', 'pink hairband', '263,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(264, 3, '蝴蝶结发带', 'bow hairband', '264,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(265, 3, '猫头饰', 'cat hair ornament', '265,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(266, 3, '音符发饰', 'musical note hair ornament', '266,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(267, 3, '胡萝卜发饰', 'carrot hair ornament', '267,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(268, 3, '紫色发带', 'purple hairband', '268,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(269, 3, '发珠', 'hair beads', '269,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(270, 3, '多个蝴蝶结', 'multiple hair bows', '270,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(271, 3, '蝙蝠发饰', 'bat hair ornament', '271,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(272, 3, '骨发饰', 'bone hair ornament', '272,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(273, 3, '橙色发带', 'orange hairband', '273,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(274, 3, '雪花发饰', 'snowflake hair ornament', '274,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(275, 3, '头上有花', 'flower on head', '275,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(276, 3, '头上戴着花冠', 'head wreath', '276,3,1', '普通', '2023-04-18 13:56:24', '2023-04-18 13:56:24'),
	(277, 0, '构图', NULL, '277', NULL, '2023-04-18 13:58:04', '2023-04-18 13:58:05'),
	(278, 277, '镜头', NULL, '278,,277', NULL, '2023-04-18 13:58:04', '2023-04-18 13:58:05'),
	(279, 277, '视角', NULL, '279,,277', NULL, '2023-04-18 13:58:04', '2023-04-18 13:58:05'),
	(280, 277, '形式', NULL, '280,,277', NULL, '2023-04-18 13:58:04', '2023-04-18 13:58:05'),
	(281, 0, '画面效果', NULL, '281', NULL, '2023-04-18 13:58:04', '2023-04-18 13:58:05'),
	(282, 4, '爱心形瞳孔', 'heart-shaped pupils', '282,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(283, 4, '眼睛抬头', 'rolling eyes', '283,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(284, 4, '疯狂的眼睛', 'crazy eyes', '284,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(285, 4, '挑眉', 'raised eyebrows', '285,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(286, 4, '下眉毛', 'furrowed brow', '286,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(287, 4, '黑眼圈中的水平线', 'dashed eyes', '287,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(288, 4, '多色的黑眼睛', 'multicolored eyes', '288,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(289, 4, '左右眼颜色不同', 'mismatched pupils', '289,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(290, 4, '左右眼不同颜色的眼白', 'mismatched sclera', '290,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(291, 4, '没有眼睛', 'no pupils', '291,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(292, 4, '眼睛没有亮点', 'empty eyes', '292,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(293, 4, '白眼球', 'blank eyes', '293,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(294, 4, '瞳孔扩张', 'dilated pupils', '294,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(295, 4, '漆黑的眼睛', 'hollow eyes', '295,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(296, 4, '瞳孔收缩', 'constricted pupils', '296,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(297, 4, '符号眼', 'symbol-shaped pupils', '297,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(298, 4, '星形眼睛', 'star-shaped pupils', '298,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(299, 4, 'X 形眼睛', 'x-shaped pupils', '299,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(300, 4, '纽扣眼', 'button eyes', '300,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(301, 4, '眼睛反射', 'eye reflection', '301,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(302, 4, '闭上眼睛', 'closed eyes', '302,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(303, 4, '一只眼睛闭着', 'one eye closed', '303,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(304, 4, '眼睛半闭', 'half-closed eyes', '304,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(305, 4, '闭眼', 'eyes closed', '305,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(306, 4, '闭一只眼', 'wince', '306,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(307, 4, '吊眼角', 'tsurime', '307,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(308, 4, '盯着看', 'eyeball', '308,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(309, 4, '眼泪', 'tears', '309,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(310, 4, '渐变瞳色', 'gradient eyes', '310,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(311, 4, '吐舌鬼脸', 'aqua eyes', '311,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(312, 4, '睁着眼落泪', 'crying with eyes open', '312,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(313, 4, '发光的双眼', 'glowing eyes', '313,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(314, 4, '半闭的眼睛(单眼)', 'half-closed eye', '314,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(315, 4, '开心的眼泪', 'happy tears', '315,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(316, 4, '星星眼', 'sparkling eyes', '316,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(317, 4, '轻蔑/怒视', 'glaring', '317,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(318, 4, '流泪', 'streaming tears', '318,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(319, 4, '挡在头发下的眉毛', 'eyebrows behind hair', '319,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(320, 4, '空洞眼睛', 'empty eyes', '320,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(321, 4, '睁大眼睛', 'wide eyes', '321,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(322, 4, '闭上一只眼', 'one eye closed', '322,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(323, 4, '半闭眼睛', 'half-closed eyes', '323,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(324, 4, '渐变眼', 'gradient_eyes', '324,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(325, 4, '水汪汪大眼', 'aqua eyes', '325,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(326, 4, '翻白眼', 'rolling eyes', '326,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(327, 4, '斗鸡眼', 'cross-eyed', '327,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(328, 4, '猫眼', 'slit pupils', '328,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(329, 4, '布满血丝的眼睛', 'bloodshot eyes', '329,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(330, 4, '发光眼睛', 'glowing eyes', '330,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(331, 4, '吊眼角', 'tsurime', '331,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(332, 4, '垂眼角', 'tareme', '332,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(333, 4, '恶魔眼', 'devil eyes', '333,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(334, 4, '收缩的瞳孔', 'constricted pupils', '334,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(335, 4, '魔瞳', 'devil pupils', '335,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(336, 4, '蛇瞳', 'snake pupils', '336,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(337, 4, '异色瞳', 'heterochromia', '337,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(338, 4, '紫眼', 'purple eyes', '338,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(339, 4, '红眼', 'red eyes', '339,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(340, 4, '竖瞳孔/猫眼', 'slit pupils', '340,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(341, 4, '白眼', 'white eyes', '341,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(342, 4, '金眼', 'yellow eyes', '342,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(343, 4, '下垂的眼睛', 'tareme', '343,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(344, 4, '三白眼', 'sanpaku', '344,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(345, 4, '上翘的眼睛', 'upturned eyes', '345,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(346, 4, '睁开眼睛', 'wide-eyed', '346,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(347, 4, '眼圈', 'ringed eyes', '347,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(348, 4, '闪闪发光瞳', 'pupils sparkling', '348,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(349, 4, '花形瞳', 'flower-shaped pupils', '349,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(350, 4, '爱心瞳', 'heart-shaped pupils', '350,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(351, 4, '异色瞳', 'heterochromia', '351,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(352, 4, '美瞳', 'color contact lenses', '352,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(353, 4, '长睫毛', 'longeyelashes', '353,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(354, 4, '彩色睫毛', 'colored eyelashes', '354,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(355, 4, '眼下痣', 'mole under eye', '355,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(356, 4, '实心圆眼睛', 'solid circle eyes', '356,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(357, 4, '心形眼', 'heart-shaped eyes', '357,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(358, 4, '晕眼', '@ @', '358,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(359, 4, '斗鸡眼', 'cross-eyed', '359,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(360, 4, '橙色的眼镜', 'orange eyes', '360,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(361, 4, '粉红色的眼睛', 'pink eyes', '361,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(362, 4, '琥珀色眼', 'amber eyes', '362,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(363, 4, '吃豆人形眼', 'pac-man eyes', '363,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(364, 4, '一字型瞳孔/蛙眼', 'horizontal pupils', '364,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(365, 4, '钻石形瞳孔', 'diamond-shaped pupils', '365,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(366, 4, '花形瞳孔', 'flower-shaped pupils', '366,4,1', '普通', '2023-04-18 14:02:21', '2023-04-18 14:02:21'),
	(367, 5, '动物耳朵', 'animal ears', '367,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(368, 5, '狐狸耳朵', 'fox ears', '368,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(369, 5, '猫耳', 'cat ears', '369,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(370, 5, '狗耳', 'dog ears', '370,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(371, 5, '老鼠耳朵', 'mouse ears', '371,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(372, 5, '尖耳', 'pointy ears', '372,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(373, 5, '心形耳环', 'heart earrings', '373,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(374, 5, '环状耳环', 'hoop earrings', '374,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(375, 5, '水晶耳环', 'crystal earrings', '375,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(376, 5, '耳环', 'earrings', '376,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(377, 5, '月牙耳环', 'crescent earrings', '377,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(378, 5, '猫耳式耳机', 'cat ear headphones', '378,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(379, 5, '蝙蝠耳朵', 'bat ears', '379,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(380, 5, '浣熊耳朵', 'raccoon ears', '380,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(381, 5, '尖尖的长耳朵', 'long pointy ears', '381,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(382, 5, '遮住耳朵', 'covering ears', '382,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(383, 5, '熊耳朵', 'bear ears', '383,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(384, 5, '兔子耳朵', 'rabbit ears', '384,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(385, 5, '牛耳朵', 'cow ears', '385,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(386, 5, '鹿耳朵', 'deer ears', '386,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(387, 5, '鼬耳朵', 'ferret ears', '387,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(388, 5, '山羊耳朵', 'goat ears', '388,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(389, 5, '马耳', 'horse ears', '389,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(390, 5, '兽耳萝莉模式', 'kemonomimi mode', '390,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(391, 5, '狮子耳朵', 'lion ears', '391,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(392, 5, '猴耳', 'monkey ears', '392,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(393, 5, '熊猫耳朵', 'panda ears', '393,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(394, 5, '皮卡丘耳朵', 'pikachu ears', '394,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(395, 5, '猪耳朵', 'pig ears', '395,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(396, 5, '羊耳', 'sheep ears', '396,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(397, 5, '松鼠耳朵', 'squirrel ears', '397,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(398, 5, '虎耳', 'tiger ears', '398,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(399, 5, '狼耳朵', 'wolf ears', '399,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(400, 5, '仿制的动物耳朵', 'fake animal ears', '400,5,1', '普通', '2023-04-18 14:02:55', '2023-04-18 14:02:55'),
	(401, 6, '浓装', 'makeup', '401,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(402, 6, '食物在脸上', 'food on face', '402,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(403, 6, '闻', 'smelling', '403,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(404, 6, '鼻血', 'nosebleed', '404,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(405, 6, '咬牙', 'clenched teeth', '405,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(406, 6, '张口', 'open mouth', '406,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(407, 6, '努嘴', 'pout', '407,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(408, 6, '叹气', 'sigh', '408,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(409, 6, '微笑', 'smile', '409,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(410, 6, '浅笑', 'light smile', '410,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(411, 6, '露齿而笑', 'grin', '411,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(412, 6, '坏笑', 'evil smile', '412,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(413, 6, '生气的', 'angry', '413,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(414, 6, '苦恼的', 'annoyed', '414,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(415, 6, '疯狂的', 'crazy', '415,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(416, 6, '害羞的', 'shy', '416,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(417, 6, '尴尬的', 'embarrassed', '417,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(418, 6, '脸红的', 'blush', '418,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(419, 6, '困乏的', 'sleepy', '419,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(420, 6, '悲伤的', 'sad', '420,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(421, 6, '喝醉的', 'drunk', '421,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(422, 6, '皱眉/蹙额', 'frown', '422,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(423, 6, '尖牙', 'fangs', '423,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(424, 6, '舌头', 'tongue', '424,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(425, 6, '没有鼻子', 'no nose', '425,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(426, 6, '唾液', 'saliva', '426,6,1', '普通', '2023-04-18 14:03:31', '2023-04-18 14:03:31'),
	(427, 6, '胡子', 'facial hair', '427,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(428, 6, '用手指做出笑脸', 'fingersmile', '428,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(429, 6, '嘴咬住', 'mouth hold', '429,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(430, 6, '嘴唇微张', 'parted lips', '430,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(431, 6, '闭嘴', 'closed mouth', '431,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(432, 6, '吐舌头', 'tongue out', '432,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(433, 6, '舔嘴唇', 'licking lips', '433,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(434, 6, '疼痛', 'pain', '434,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(435, 6, '哭', 'crying', '435,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(436, 6, '气得冒烟(漫画)', 'fume', '436,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(437, 6, '厌恶的怪相', 'grimace', '437,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(438, 6, '尖叫', 'screaming', '438,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(439, 6, 'V形眉(表高傲或愤怒)', 'v-shaped eyebrows', '439,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(440, 6, '害怕的', 'scared', '440,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(441, 6, '怒视/嫌弃/不满', 'scowl', '441,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(442, 6, '严肃的', 'serious', '442,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(443, 6, '要哭的表情', 'tearing up', '443,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(444, 6, '无聊的', 'bored', '444,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(445, 6, '消沉(表情)', 'gloom (expression)', '445,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(446, 6, '嫉妒的', 'jealous', '446,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(447, 6, '轻蔑的眼神', 'jitome', '447,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(448, 6, '不安的', 'nervous', '448,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(449, 6, '不安地微笑', 'nervous smile', '449,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(450, 6, '阴沉脸', 'shaded', '450,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(451, 6, '脸色苍白', 'turn pale', '451,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(452, 6, '无口', 'expressionless', '452,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(453, 6, '表情', 'expressions', '453,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(454, 6, '失神', 'unconscious', '454,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(455, 6, '明亮的瞳孔', 'bright pupils', '455,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(456, 6, '耳红', 'ear blush', '456,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(457, 6, '憋气', 'holding breath', '457,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(458, 6, '撅起的嘴唇', 'puckered lips', '458,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(459, 6, '诱人的微笑', 'seductive smile', '459,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(460, 6, '笑脸', 'smiley', '460,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(461, 6, '傻笑/得意的笑', 'smirk', '461,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(462, 6, '得意脸', 'doyagao', '462,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(463, 6, '慌乱的', 'flustered', '463,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(464, 6, '整张脸泛红', 'full blush', '464,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(465, 6, '眼里冒爱心', 'heart in eye', '465,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(466, 6, '喘粗气', 'heavy breathing', '466,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(467, 6, '呻吟', 'moaning', '467,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(468, 6, '得意脸', 'smug', '468,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(469, 6, '惊讶或无语到喷了', 'spit take', '469,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(470, 6, '惊讶', 'surprised', '470,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(471, 6, '傲娇', 'tsundere', '471,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(472, 6, '流口水', 'drooling', '472,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(473, 6, '诱惑的表情', 'torogao', '473,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(474, 6, '阿嘿颜', 'ahegao', '474,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(475, 6, '下流的表情', 'naughty face', '475,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(476, 6, '下流的表情', 'naughty', '476,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(477, 6, '忍耐的表情', 'endured face', '477,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(478, 6, '眼中闪现强烈的情感', 'glint', '478,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(479, 6, '快乐/幸福', 'happy', '479,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(480, 6, '在笑的', 'laughing', '480,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(481, 6, '嚣张脸', 'troll', '481,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(482, 6, '病娇', 'yandere', '482,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(483, 6, '唾液拉丝', 'saliva trail', '483,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(484, 6, '朱唇', 'red lips', '484,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(485, 6, '虎牙状', 'skin fang', '485,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(486, 6, '露出上排牙齿', 'upper teeth', '486,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(487, 6, '虎牙', 'fang', '487,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(488, 6, '露出虎牙/露出尖牙', 'fang out', '488,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(489, 6, '长舌头', 'long tongue', '489,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(490, 6, '额头', 'forehead', '490,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(491, 6, '淡淡的腮红', 'light blush', '491,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(492, 6, '脸贴脸', 'cheek-to-cheek', '492,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(493, 6, '鼓着腮帮', 'cheek bulge', '493,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(494, 6, '捏脸颊', 'cheek pinching', '494,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(495, 6, '戳脸颊', 'cheek poking', '495,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(496, 6, '扯脸颊', 'cheek pull', '496,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(497, 6, '抬下巴', 'chin grab', '497,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(498, 6, '遮住眼睛', 'covering eyes', '498,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(499, 6, '挡住脸', 'covering', '499,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(500, 6, '挡住嘴巴', 'covering mouth', '500,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(501, 6, '脸贴脸', 'face-to-face', '501,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(502, 6, '二人面对面(脸贴得很近)', 'facing another', '502,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(503, 6, '额头贴额头', 'forehead-to-forehead', '503,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(504, 6, '牙', 'teeth', '504,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(505, 6, '兴奋', 'excited', '505,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(506, 6, '害羞', 'nose blush', '506,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(507, 6, '失神', 'expressionless eyes', '507,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(508, 6, '青筋', 'anger vein', '508,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(509, 6, '表情贴纸', 'blush stickers', '509,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(510, 6, '整张脸泛红', 'full-face blush', '510,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(511, 6, '疑惑', 'confused', '511,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(512, 6, '有决心的', 'determined', '512,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(513, 6, '失望的', 'disappointed', '513,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(514, 6, '蔑视', 'disdain', '514,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(515, 6, '恶心', 'disgust', '515,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(516, 6, '绝望', 'despair', '516,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(517, 6, '嫉妒', 'envy', '517,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(518, 6, '邪恶', 'evil', '518,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(519, 6, '以手掩面', 'facepalm', '519,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(520, 6, '沮丧', 'frustrated', '520,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(521, 6, '有罪的', 'guilt', '521,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(522, 6, '库布里克凝视', 'kubrick stare', '522,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(523, 6, '孤独的', 'lonely', '523,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(524, 6, '扬起的眉毛', 'raised eyebrow', '524,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(525, 6, '强硬的表情', 'rape face', '525,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(526, 6, '压抑的/郁闷的', 'depressed', '526,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(527, 6, '恐慌的', 'panicking', '527,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(528, 6, '担忧的', 'worried', '528,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(529, 6, '累', 'tired', '529,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(530, 6, '闷闷不乐', 'sulking', '530,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(531, 6, '思考', 'thinking', '531,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(532, 6, '沉思的', 'pensive', '532,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(533, 6, '气愤', 'upset', '533,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(534, 6, '疯狂地笑', 'crazy smile', '534,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(535, 6, '强迫笑', 'forced smile', '535,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(536, 6, '格拉斯哥微笑', 'glasgow smile', '536,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(537, 6, '苦笑', 'sad smile', '537,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(538, 6, '憋笑', 'stifled laugh', '538,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(539, 6, '惊讶到掉色', 'color drain', '539,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(540, 6, '恐惧表情', 'horrified', '540,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(541, 6, '啜泣', 'sobbing', '541,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(542, 6, '伸出舌头', 'oral invitation', '542,6,1', '普通', '2023-04-18 14:03:32', '2023-04-18 14:03:32'),
	(544, 278, '镜头光晕', 'lens flare', '544,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(545, 278, '过曝', 'overexposure', '545,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(546, 278, '背景散焦', 'bokeh', '546,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(547, 278, '焦散', 'caustics', '547,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(548, 278, '衍射十字星', 'diffraction spikes', '548,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(549, 278, '正前缩距透视法', 'foreshortening', '549,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(550, 278, '集中线', 'emphasis lines', '550,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(551, 278, '卫星鸟瞰', 'satellite image', '551,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(552, 278, '微距照片', 'macro photo', '552,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(553, 278, '360 度视角', '360 view', '553,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(554, 278, '广角', 'Wide-Angle', '554,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(555, 278, '超广角', 'Ultra-Wide Angle', '555,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(556, 278, '人眼视角拍摄', 'Eye-Level Shot', '556,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(557, 278, '人眼视角拍摄', 'Eye-Level Shot', '557,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(558, 278, '光圈 F1.2', 'f/1.2', '558,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(559, 278, '光圈 F1.8', 'f/1.8', '559,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(560, 278, '光圈 F2.8', 'f/2.8', '560,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(561, 278, '光圈 F4.0', 'f/4.0', '561,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(562, 278, '光圈 F16', 'f/16', '562,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(563, 278, '焦距 35mm', '35mm', '563,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(564, 278, '焦距 85mm', '85mm', '564,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(565, 278, '焦距 135mm', '135mm', '565,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(566, 278, '尼康', 'Nikon', '566,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(567, 278, '佳能', 'Canon', '567,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(568, 278, '富士', 'Fujifilm', '568,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(569, 278, '哈数', 'Hasselblad', '569,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(570, 278, '索尼镜头', 'Sony FE', '570,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(571, 278, '索尼大师镜头', 'Sony FE GM', '571,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(572, 278, '索尼大师镜头', 'Sony FE GM', '572,278,277', '风格', '2023-04-18 14:04:04', '2023-04-18 14:04:04'),
	(573, 279, '第一人称视角', 'first-person view', '573,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(574, 279, '主观视角', 'pov', '574,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(575, 279, '三视图', 'three sided view', '575,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(576, 279, '多视图', 'multiple views', '576,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(577, 279, '插入画面', 'cut-in', '577,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(578, 279, '前景模糊', 'blurry foreground', '578,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(579, 279, '特写镜头', 'close-up', '579,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(580, 279, '七分身镜头', 'cowboy shot', '580,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(581, 279, '德式倾斜镜头', 'dutch angle', '581,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(582, 279, '鱼眼镜头', 'fisheye', '582,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(583, 279, '线影法(纹理)', 'hatching (texture)', '583,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(584, 279, '远景透视画法', 'vanishing point', '584,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(585, 279, '广角镜头', 'wide shot', '585,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(586, 279, '俯视镜头', 'from above', '586,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(587, 279, '背影', 'from behind', '587,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(588, 279, '仰视镜头', 'from below', '588,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(589, 279, '室外看向室内(的镜头)', 'from outside', '589,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(590, 279, '角色的侧面', 'from side', '590,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(591, 279, '大气距离感', 'atmospheric perspective', '591,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(592, 279, '全景', 'panorama', '592,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(593, 279, '透视画法', 'perspective', '593,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(594, 279, '经过旋转的', 'rotated', '594,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(595, 279, '横向显示的', 'sideways', '595,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(596, 279, '倒挂的', 'upside-down', '596,279,277', '风格', '2023-04-18 14:06:25', '2023-04-18 14:06:25'),
	(597, 280, '残像', 'afterimage', '597,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(598, 280, '边框', 'border', '598,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(599, 280, '画框', 'framed', '599,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(600, 280, '一部分画到了背景框外面', 'outside border', '600,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(601, 280, '褪色边框', 'fading border', '601,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(602, 280, '背景或画框是圆角', 'rounded corners', '602,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(603, 280, '相机取景框', 'viewfinder', '603,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(604, 280, '图表', 'chart', '604,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(605, 280, '人设图', 'character chart', '605,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(606, 280, '设定图', 'reference sheet', '606,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(607, 280, '图表', 'diagram', '607,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(608, 280, '动作演示图', 'move chart', '608,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(609, 280, '关系表', 'relationship graph', '609,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(610, 280, '座次表', 'seating chart', '610,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(611, 280, '属性栏/状态表', 'stats', '611,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(612, 280, '拼贴画', 'collage', '612,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(613, 280, '小图拼接', 'column lineup', '613,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(614, 280, '胸围图', 'bust chart', '614,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(615, 280, '遭到裁剪', 'cropped', '615,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(616, 280, '假的滚动条', 'fake scrollbar', '616,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(617, 280, '头部脱框', 'head out of frame', '617,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(618, 280, '脱框', 'out of frame', '618,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(619, 280, '脚部脱框', 'feet out of frame', '619,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(620, 280, '等轴', 'isometric', '620,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(621, 280, '宽银幕格式', 'letterboxed', '621,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(622, 280, '柱状画布背景', 'pillarboxed', '622,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(623, 280, '一排人', 'lineup', '623,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(624, 280, '马赛克艺术', 'mosaic art', '624,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(625, 280, '马赛克拼图', 'photomosaic', '625,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(626, 280, '大量留白', 'negative space', '626,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(627, 280, '附图', 'omake', '627,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(628, 280, '部分水下拍摄', 'partially underwater shot', '628,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(629, 280, '社交媒体整合', 'social media composition', '629,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(630, 280, '左右对称', 'symmetry', '630,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(631, 280, '两极对称', 'polar opposites', '631,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(632, 280, '对称旋转', 'rotational symmetry', '632,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(633, 280, '立绘样式', 'tachi-e', '633,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(634, 280, '裁剪标记', 'trim marks', '634,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(635, 280, '人物立绘缩放(剪影)图层', 'zoom layer', '635,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(636, 280, '类似海报或杂志的插图效果', 'projected inset', '636,280,277', '风格', '2023-04-18 14:07:14', '2023-04-18 14:07:14'),
	(637, 281, '动态模糊', 'motion blur', '637,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(638, 281, '色差', 'chromatic aberration', '638,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(639, 281, '闪耀效果', 'sparkle', '639,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(640, 281, 'JPEG 压缩失真', 'jpeg artifacts', '640,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(641, 281, '模糊的', 'blurry', '641,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(642, 281, '电影光效', 'cinematic lighting', '642,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(643, 281, '荧光', 'glowing light', '643,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(644, 281, '神圣感顶光', 'god rays', '644,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(645, 281, '光线追踪', 'ray tracing', '645,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(646, 281, '反射光', 'reflection light', '646,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(647, 281, '逆光', 'backlighting', '647,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(648, 281, '混合', 'blending', '648,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(649, 281, '盛开', 'bloom', '649,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(650, 281, '明暗对比', 'chiaroscuro', '650,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(651, 281, '色差滥用', 'chromatic aberration abuse', '651,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(652, 281, '背景虚化', 'depth of field', '652,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(653, 281, '抖动', 'dithering', '653,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(654, 281, '立绘阴影', 'drop shadow', '654,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(655, 281, '胶片颗粒感/老电影滤镜', 'film grain', '655,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(656, 281, '富士色彩', 'Fujicolor', '656,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(657, 281, '半调风格', 'halftone', '657,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(658, 281, '图像填充', 'image fill', '658,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(659, 281, '体现运动的线', 'motion lines', '659,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(660, 281, '多重单色', 'multiple monochrome', '660,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(661, 281, '视错觉', 'optical illusion', '661,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(662, 281, '互补色', 'anaglyph', '662,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(663, 281, '立体画', 'stereogram', '663,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(664, 281, '扫描线', 'scanlines', '664,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(665, 281, '剪影', 'silhouette', '665,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(666, 281, '速度线', 'speed lines', '666,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13'),
	(667, 281, '晕影', 'vignetting', '667,281', '风格', '2023-04-18 14:08:13', '2023-04-18 14:08:13');

-- 正在导出表  agent2_prd.prompt_tags 的数据：~16 rows (大约)
INSERT INTO `prompt_tags` (`id`, `name`, `tag_type`, `desc`, `sort`, `created_at`, `updated_at`) VALUES
	(1, '日常办公', 1, '日常办公', 2, '2023-07-11 15:08:10', '2023-07-11 15:08:10'),
	(2, '调研分析', 1, '调研分析', 3, '2023-07-11 15:08:10', '2023-07-11 15:08:10'),
	(3, '运营提效', 1, '运营提效', 5, '2023-07-11 15:08:10', '2023-07-11 15:08:10'),
	(4, '开发辅助', 1, '开发辅助', 4, '2024-03-21 10:05:52', '2024-03-21 10:05:52'),
	(5, '知识学习', 1, '知识学习', 6, '2024-03-21 10:05:52', '2024-03-21 10:05:52'),
	(6, '其他', 1, '其他', 7, '2024-03-21 10:05:52', '2024-03-21 10:05:52'),
	(7, '数字人', 1, '数字人', 1, '2024-03-21 16:20:20', '2024-03-21 16:20:22'),
	(8, '新闻资讯', 5, '新闻资讯', 1, '2024-03-29 15:38:14', '2024-03-29 15:38:18'),
	(9, '生活服务', 5, '生活服务', 2, '2024-03-29 15:38:21', '2024-03-29 15:38:23'),
	(10, '交通出行', 5, '交通出行', 3, '2024-03-29 15:38:26', '2024-03-29 15:38:28'),
	(11, '体育健康', 5, '体育健康', 4, '2024-03-29 15:38:33', '2024-03-29 15:38:36'),
	(12, '金融', 5, '金融', 5, '2024-03-29 15:38:39', '2024-03-29 15:38:41'),
	(13, '工具', 5, '工具', 6, '2024-03-29 15:38:45', '2024-03-29 15:38:47'),
	(14, '视频', 5, '视频', 7, '2024-03-29 15:38:50', '2024-03-29 15:38:52'),
	(15, '图片', 5, '图片', 8, '2024-03-29 15:38:55', '2024-03-29 15:38:57'),
	(16, '其他', 5, '其他', 9, '2024-03-29 15:39:00', '2024-03-29 15:39:02');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;


INSERT INTO agent2_prd.prompt_user (id, uid, name, src, email, phone_number, avatar, api_key, is_deleted, status, created_at, updated_at, inner_src, salt, password) VALUES (1, '1', 'admin', 'custom_agent', '', '', '', null, 1, 1, '2024-09-14 20:26:38', '2024-09-14 20:26:40', 1, 'd27ed4d1603833e83a187754285fdfa2', 'fb79453aa8677e1becf7a53612782e73eaa5906161126685631afd8ef063b1fa');

INSERT INTO agent2_prd.team_member_role (id, team_id, user_id, team_role_id, is_deleted, created_at, updated_at) VALUES (1779, 1516, 1, 1, 1, '2024-09-12 20:31:15', '2024-09-12 20:31:15');

INSERT INTO agent2_prd.channels (id, channel_name, identifier, description, used_status, internal_net, agent_relation_nums, created_at, updated_at, sort, image_url, show_all) VALUES (1, '智能体商店', 'official', '发布后，智能体将展示在Agent平台的智能体市场中，为你带来更多曝光。', 1, 1, 0, '2024-05-10 15:21:08', '2024-06-11 17:59:32', 90, '/privatization/t011b474c91ef6aba27.png', 1);

INSERT INTO `agent2_prd`.`admin_member_role` (`id`, `user_id`, `role_id`) VALUES ('1', '1', '1');

INSERT INTO agent2_prd.team (id, name, is_deleted, last_op_uid, created_at, updated_at, team_type, create_type, src, encrypt_public_key) VALUES (1516, '个人项目', 1, 1, '2024-09-29 21:01:50', '2024-09-29 21:01:50', 4, 0, 'custom_agent', '');


INSERT INTO `agent` (`id`, `name`, `description`, `icon`, `llm_model`, `llm_param`, `llm_id`, `llm_icon`, `llm_provider`, `voice_type`, `audio_automatic_play`, `audio_status`, `digit_avatar_url`, `digit_avatar_background`, `digit_avatar_status`, `digit_image_status`, `audio_input_status`, `prompt`, `greeting_message`, `greeting_question`, `user_id`, `public_user_id`, `team_id`, `publish_status`, `public_time`, `publish_time`, `is_suggestion`, `suggestion`, `fork_nums`, `template_check_status`, `template_check_id`, `is_deleted`, `created_at`, `updated_at`, `is_privacy`, `openapi_status`, `strategy`, `openapi_admin_status`, `iframe_admin_status`) VALUES (2244, '首页智能体创建', '功能描述：首页智能体创建是指在平台的首页上为用户创建一个智能体，该智能体能够理解和回应用户的需求，提供个性化的服务和建议。\n\n输入描述：用户的基本信息、兴趣爱好、常用功能、期望的交互方式等。\n\n输出描述：智能体能够根据用户的输入，提供个性化的建议、解答疑问以及执行相应的操作。\n\n使用样例：用户在注册时提供个人喜好和需求等信息，智能体根据这些信息在首页为用户展示个性化的推荐内容，如新闻、商品、服务等，并能够根据用户的进一步互动，持续学习和优化推荐内容。', 'https://beijing.xstore.qihu.com/qiyuan-hongtu/6cc17b4c-a38a-4436-b935-684b895bef79.png', 'gpt-3.5-turbo-16k', '{"top_k": 1, "top_p": 0.6, "temperature": 0.4, "max_tokens": 4096, "num_beams": 1, "repetition_penalty": 1.05}', NULL, NULL, 'zhinao', 1, 2, 2, '', '', 2, 2, 2, '# 角色：智能体创建工程师\n- 描述：作为专注于智能体创建的工程师，我的主要任务是根据用户的具体描述来设计和定义智能体的名称和描述。我致力于提供精确和专业的服务，确保智能体的创建满足用户的需求。\n\n## 限制：\n1. 如果用户提出的问题与智能体创建无关，我将告知用户我只能提供智能体创建相关的帮助。\n2. 我将确保在进行任何智能体创建的工作之前，获得用户的明确确认。\n3. 智能体的名称和描述将使用中文，并遵循用户的预期进行创建。\n4. 在用户确认之前，我不会使用任何工具或生成输出。\n\n## 输出格式：\n- 确认格式：在进行下一步之前，我会这样询问用户：“好的，我为你的机器人取了一个名字叫[名称]，描述是[描述]。这样的名字和描述你是否满意呢？”\n- 无关问题回复格式：如果用户的问题与智能体创建无关，我会这样回复：“您好，我只能回答智能体创建相关的咨询。关于其他问题，我可能无法提供帮助。”\n- 迷茫时的回复格式：如果我不知道用户想要我做什么，我会这样回复：“我不知道你具体想要我做什么，Agent 的官方文档：https://doc.agent.qihoo.net/intro.html。”\n\n## 工作流程：\n1. 询问用户他们希望智能体具备哪些特性或用途。\n2. 根据用户提供的信息，设计智能体的名称和描述。\n3. 向用户展示初步的名称和描述，并请求确认。如果用户不满意，换个名称和描述再次展示确认。\n4. 用户确认后，继续进行智能体的创建工作；\n\n## 示例：\n- 用户提出一个与智能体创建无关的问题。\n- 我的回复是：“您好，我只能回答智能体创建相关的咨询。关于其他问题，我可能无法提供帮助。”\n\n## 初始化：\n当用户开始对话时，我会这样欢迎他们：“您好！我是智能体创建工程师，专注于根据您的需求为您定制智能体。请告诉我您希望您的智能体具备哪些特性或用途，我将为您设计一个独特的名称和描述。”', '', '[]', 18, 18, 301, 1, NULL, NULL, 2, '', 0, 4, 0, 1, '2024-05-20 14:23:30', '2024-12-24 15:24:23', 1, 1, 2, 1, 1);